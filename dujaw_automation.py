#!/usr/bin/env python3
"""
Automatización específica para dujaw.com
Captura el flujo completo de login y gestión de correos
"""

import asyncio
import json
import time
from datetime import datetime
from playwright.async_api import async_playwright
import re


class DujawAutomation:
    def __init__(self):
        self.network_requests = []
        self.network_responses = []
        self.base_url = "https://dujaw.com"
        self.target_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.password = "unlockgs2024"
        
    async def setup_browser(self):
        """Configura el navegador con interceptación de red"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # Modo visual para ver qué pasa
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        self.page = await self.context.new_page()
        
        # Configurar interceptación de red
        await self.setup_network_interception()
        
    async def setup_network_interception(self):
        """Configura la interceptación completa de red"""
        
        async def handle_request(request):
            """Captura todas las peticiones HTTP"""
            try:
                headers = await request.all_headers()
                post_data = request.post_data
                
                request_data = {
                    'timestamp': datetime.now().isoformat(),
                    'method': request.method,
                    'url': request.url,
                    'headers': headers,
                    'post_data': post_data,
                    'resource_type': request.resource_type
                }
                self.network_requests.append(request_data)
                
                # Mostrar solo las llamadas importantes
                if request.resource_type in ['xhr', 'fetch', 'document'] or '/api/' in request.url:
                    print(f"📤 {request.method} {request.url}")
                    if post_data:
                        print(f"   📝 Data: {post_data[:200]}...")
                        
            except Exception as e:
                print(f"Error capturando request: {e}")
                
        async def handle_response(response):
            """Captura todas las respuestas HTTP"""
            try:
                headers = await response.all_headers()
                
                # Solo capturar contenido de respuestas relevantes
                content = None
                if response.request.resource_type in ['xhr', 'fetch', 'document']:
                    try:
                        content = await response.text()
                    except:
                        content = "Error reading response content"
                
                response_data = {
                    'timestamp': datetime.now().isoformat(),
                    'status': response.status,
                    'url': response.url,
                    'headers': headers,
                    'content': content,
                    'resource_type': response.request.resource_type
                }
                self.network_responses.append(response_data)
                
                # Mostrar solo respuestas importantes
                if response.request.resource_type in ['xhr', 'fetch', 'document'] or '/api/' in response.url:
                    print(f"📥 {response.status} {response.url}")
                    
            except Exception as e:
                print(f"Error capturando response: {e}")
        
        self.page.on('request', handle_request)
        self.page.on('response', handle_response)
        
    async def navigate_and_login(self):
        """Navega al sitio y realiza el login"""
        print(f"🌐 Navegando a: {self.target_url}")
        
        try:
            # Navegar a la URL
            await self.page.goto(self.target_url, wait_until='networkidle', timeout=30000)
            await self.take_screenshot("01_initial_page")
            
            # Esperar un poco para que cargue completamente
            await asyncio.sleep(2)
            
            # Buscar campo de contraseña
            print("🔍 Buscando campo de contraseña...")
            
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[name="pass"]',
                'input[id*="password"]',
                'input[id*="pass"]',
                '#password',
                '.password'
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = await self.page.wait_for_selector(selector, timeout=3000)
                    if password_field:
                        print(f"✅ Campo de contraseña encontrado: {selector}")
                        break
                except:
                    continue
                    
            if password_field:
                # Ingresar contraseña
                await password_field.fill(self.password)
                print("📝 Contraseña ingresada")
                await self.take_screenshot("02_password_entered")
                
                # Buscar botón de submit
                submit_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:has-text("Login")',
                    'button:has-text("Enter")',
                    'button:has-text("Submit")',
                    'button:has-text("Unlock")',
                    '.submit-btn',
                    '.login-btn',
                    '#submit',
                    '#login'
                ]
                
                submit_button = None
                for selector in submit_selectors:
                    try:
                        submit_button = await self.page.wait_for_selector(selector, timeout=2000)
                        if submit_button:
                            print(f"✅ Botón de submit encontrado: {selector}")
                            break
                    except:
                        continue
                        
                if submit_button:
                    # Hacer click en submit
                    await submit_button.click()
                    print("🔘 Botón de login clickeado")
                    
                    # Esperar a que se procese el login
                    await asyncio.sleep(3)
                    await self.page.wait_for_load_state('networkidle')
                    await self.take_screenshot("03_after_login")
                    
                    return True
                else:
                    # Intentar presionar Enter
                    await password_field.press('Enter')
                    print("⌨️ Enter presionado en campo de contraseña")
                    await asyncio.sleep(3)
                    await self.page.wait_for_load_state('networkidle')
                    await self.take_screenshot("03_after_enter")
                    return True
            else:
                print("❌ No se encontró campo de contraseña")
                # Tomar screenshot para debug
                await self.take_screenshot("debug_no_password_field")
                
                # Mostrar el HTML para debug
                content = await self.page.content()
                print("🔍 Contenido de la página (primeros 500 chars):")
                print(content[:500])
                
                return False
                
        except Exception as e:
            print(f"❌ Error durante navegación/login: {e}")
            await self.take_screenshot("error_navigation")
            return False
            
    async def explore_mailbox(self):
        """Explora la funcionalidad del mailbox"""
        print("\n📧 Explorando funcionalidades del mailbox...")
        
        try:
            # Esperar a que cargue la página
            await asyncio.sleep(2)
            
            # Buscar elementos interactivos comunes
            interactive_selectors = [
                'a[href*="mail"]',
                'a[href*="inbox"]',
                'a[href*="compose"]',
                'button',
                '.btn',
                '.button',
                '[onclick]',
                'a[href*="delete"]',
                'a[href*="read"]',
                'a[href*="send"]'
            ]
            
            print("🔍 Buscando elementos interactivos...")
            found_elements = []
            
            for selector in interactive_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        text = await element.inner_text()
                        href = await element.get_attribute('href')
                        onclick = await element.get_attribute('onclick')
                        
                        if text and text.strip():
                            found_elements.append({
                                'selector': selector,
                                'text': text.strip()[:50],
                                'href': href,
                                'onclick': onclick
                            })
                except:
                    continue
                    
            print(f"✅ Encontrados {len(found_elements)} elementos interactivos:")
            for elem in found_elements[:10]:  # Mostrar solo los primeros 10
                print(f"   📌 {elem['text']} ({elem['selector']})")
                
            # Intentar hacer click en algunos elementos para generar más tráfico de red
            clickable_texts = ['inbox', 'mail', 'compose', 'send', 'delete', 'refresh', 'reload']
            
            for elem in found_elements[:5]:  # Solo los primeros 5 para no saturar
                text_lower = elem['text'].lower()
                if any(keyword in text_lower for keyword in clickable_texts):
                    try:
                        print(f"🔘 Haciendo click en: {elem['text']}")
                        element = await self.page.query_selector(elem['selector'])
                        if element:
                            await element.click()
                            await asyncio.sleep(2)
                            await self.page.wait_for_load_state('networkidle')
                            await self.take_screenshot(f"clicked_{elem['text'][:20]}")
                    except Exception as e:
                        print(f"   ❌ Error haciendo click: {e}")
                        
        except Exception as e:
            print(f"❌ Error explorando mailbox: {e}")
            
    async def take_screenshot(self, name: str):
        """Toma una captura de pantalla"""
        try:
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"screenshot_{timestamp}_{name}.png"
            await self.page.screenshot(path=filename, full_page=True)
            print(f"📸 Screenshot: {filename}")
        except Exception as e:
            print(f"❌ Error tomando screenshot: {e}")
            
    async def save_network_data(self):
        """Guarda los datos de red capturados"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Guardar requests
        requests_file = f"network_requests_{timestamp}.json"
        with open(requests_file, 'w', encoding='utf-8') as f:
            json.dump(self.network_requests, f, indent=2, ensure_ascii=False)
            
        # Guardar responses
        responses_file = f"network_responses_{timestamp}.json"
        with open(responses_file, 'w', encoding='utf-8') as f:
            json.dump(self.network_responses, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Datos de red guardados:")
        print(f"   📤 Requests: {requests_file} ({len(self.network_requests)} entradas)")
        print(f"   📥 Responses: {responses_file} ({len(self.network_responses)} entradas)")
        
        return requests_file, responses_file
        
    async def analyze_captured_apis(self):
        """Analiza las APIs capturadas"""
        print(f"\n🔍 ANÁLISIS DE APIs CAPTURADAS:")
        print("=" * 50)
        
        api_calls = []
        for req in self.network_requests:
            if (req['resource_type'] in ['xhr', 'fetch'] or 
                '/api/' in req['url'] or 
                req['url'].endswith('.json') or
                req['method'] in ['POST', 'PUT', 'DELETE']):
                api_calls.append(req)
                
        print(f"📊 Total de llamadas de API detectadas: {len(api_calls)}")
        
        for i, call in enumerate(api_calls, 1):
            print(f"\n{i}. {call['method']} {call['url']}")
            if call['post_data']:
                print(f"   📤 Data: {call['post_data'][:100]}...")
            print(f"   🕒 Timestamp: {call['timestamp']}")
            
        return api_calls
        
    async def cleanup(self):
        """Limpia recursos"""
        try:
            await self.browser.close()
            await self.playwright.stop()
        except:
            pass


async def main():
    """Función principal"""
    print("🚀 AUTOMATIZACIÓN DUJAW.COM")
    print("=" * 40)
    print("URL: https://dujaw.com/mailbox/<EMAIL>")
    print("Password: unlockgs2024")
    print("=" * 40)
    
    automation = DujawAutomation()
    
    try:
        # Configurar navegador
        print("🔧 Configurando navegador...")
        await automation.setup_browser()
        
        # Navegar y hacer login
        print("\n🔐 Realizando login...")
        login_success = await automation.navigate_and_login()
        
        if login_success:
            print("✅ Login completado, explorando funcionalidades...")
            
            # Explorar el mailbox
            await automation.explore_mailbox()
            
            # Esperar un poco más para capturar cualquier actividad adicional
            print("\n⏳ Esperando actividad adicional...")
            await asyncio.sleep(5)
            
        else:
            print("⚠️ Login no confirmado, pero continuando con captura...")
            
        # Analizar y guardar datos
        api_calls = await automation.analyze_captured_apis()
        requests_file, responses_file = await automation.save_network_data()
        
        print(f"\n✅ CAPTURA COMPLETADA")
        print(f"🎯 APIs detectadas: {len(api_calls)}")
        print(f"📁 Archivos generados: {requests_file}, {responses_file}")
        
        if api_calls:
            print("\n🔄 Ejecuta 'python api_generator.py' para generar código API")
        else:
            print("\n💡 Si no se detectaron APIs, revisa las capturas de pantalla para debug")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        await automation.take_screenshot("error_general")
        
    finally:
        await automation.cleanup()
        print("\n🏁 Proceso terminado")


if __name__ == "__main__":
    asyncio.run(main())
