#!/usr/bin/env python3
"""
Script para obtener correos haciendo llamadas directas a Livewire
Basado en el análisis de los elementos wire: encontrados
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


class LivewireEmailFetcher(DujawAPI):
    def __init__(self):
        super().__init__()
        
    def extraer_datos_livewire(self):
        """Extrae datos de Livewire del HTML"""
        print("🔍 Extrayendo datos de Livewire...")
        
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar elementos con wire:id
        wire_elements = soup.find_all(attrs={'wire:id': True})
        
        livewire_data = []
        
        for element in wire_elements:
            wire_id = element.get('wire:id')
            wire_initial_data = element.get('wire:initial-data')
            
            if wire_initial_data:
                try:
                    # Decodificar los datos iniciales de Livewire
                    initial_data = json.loads(wire_initial_data)
                    livewire_data.append({
                        'wire_id': wire_id,
                        'initial_data': initial_data,
                        'element_html': str(element)[:200]
                    })
                    print(f"✅ Datos Livewire encontrados: {wire_id}")
                except json.JSONDecodeError:
                    print(f"❌ Error decodificando datos de {wire_id}")
                    
        return livewire_data
        
    def hacer_llamada_livewire(self, wire_data):
        """Hace una llamada a Livewire para obtener mensajes"""
        print(f"🔄 Haciendo llamada Livewire para {wire_data['wire_id']}...")
        
        initial_data = wire_data['initial_data']
        fingerprint = initial_data.get('fingerprint', {})
        server_memo = initial_data.get('serverMemo', {})
        
        # Construir la petición Livewire
        livewire_request = {
            'fingerprint': fingerprint,
            'serverMemo': server_memo,
            'updates': [
                {
                    'type': 'callMethod',
                    'payload': {
                        'method': 'fetchMessages',
                        'params': []
                    }
                }
            ]
        }
        
        # Headers para Livewire
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # URLs posibles para Livewire
        livewire_urls = [
            f"{self.base_url}/livewire/message/{fingerprint.get('name', '')}",
            f"{self.base_url}/livewire/update",
            f"{self.base_url}/livewire/message",
            f"{self.base_url}/livewire/component/{fingerprint.get('name', '')}"
        ]
        
        for url in livewire_urls:
            try:
                print(f"🔄 Probando URL: {url}")
                response = self.session.post(url, json=livewire_request, headers=headers)
                
                print(f"📥 Respuesta: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ Respuesta JSON exitosa: {len(str(data))} chars")
                        return data
                    except:
                        print(f"⚠️ Respuesta no JSON: {response.text[:200]}...")
                        return response.text
                else:
                    print(f"❌ Error: {response.status_code} - {response.text[:100]}...")
                    
            except Exception as e:
                print(f"❌ Error en llamada: {e}")
                
        return None
        
    def probar_otros_metodos_livewire(self, wire_data):
        """Prueba otros métodos de Livewire"""
        print("🔄 Probando otros métodos de Livewire...")
        
        metodos_a_probar = [
            'getMessages',
            'loadMessages',
            'refreshMessages',
            'fetchEmails',
            'getEmails',
            'loadEmails',
            'refreshEmails',
            'getInbox',
            'loadInbox',
            'refreshInbox'
        ]
        
        initial_data = wire_data['initial_data']
        fingerprint = initial_data.get('fingerprint', {})
        server_memo = initial_data.get('serverMemo', {})
        
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        url = f"{self.base_url}/livewire/update"
        
        for metodo in metodos_a_probar:
            try:
                livewire_request = {
                    'fingerprint': fingerprint,
                    'serverMemo': server_memo,
                    'updates': [
                        {
                            'type': 'callMethod',
                            'payload': {
                                'method': metodo,
                                'params': []
                            }
                        }
                    ]
                }
                
                print(f"🔄 Probando método: {metodo}")
                response = self.session.post(url, json=livewire_request, headers=headers)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data and 'effects' in data:
                            print(f"✅ Método {metodo} exitoso!")
                            return data
                    except:
                        pass
                        
            except Exception as e:
                continue
                
        return None
        
    def buscar_endpoints_ajax_adicionales(self):
        """Busca endpoints AJAX adicionales"""
        print("🔍 Buscando endpoints AJAX adicionales...")
        
        # Endpoints específicos para sistemas de email temporal
        endpoints_especificos = [
            f"/mailbox/{self.mailbox_url.split('/')[-1]}",
            f"/api/mailbox/{self.mailbox_url.split('/')[-1]}",
            f"/fetch/{self.mailbox_url.split('/')[-1]}",
            f"/messages/{self.mailbox_url.split('/')[-1]}",
            f"/emails/{self.mailbox_url.split('/')[-1]}",
            "/api/fetch-messages",
            "/api/get-messages",
            "/fetch-messages",
            "/get-messages",
            "/check-messages",
            "/poll-messages"
        ]
        
        for endpoint in endpoints_especificos:
            try:
                full_url = f"{self.base_url}{endpoint}"
                print(f"🔍 Probando: {endpoint}")
                
                response = self.session.get(full_url)
                
                if response.status_code == 200:
                    print(f"✅ Endpoint activo: {endpoint}")
                    
                    # Verificar si contiene datos de correos
                    content_type = response.headers.get('content-type', '').lower()
                    
                    if 'json' in content_type:
                        try:
                            data = response.json()
                            if self.contiene_datos_correo(data):
                                print(f"📧 ¡Correos encontrados en {endpoint}!")
                                return data
                        except:
                            pass
                    else:
                        # Verificar si el HTML contiene información de correos
                        if self.html_contiene_correos(response.text):
                            print(f"📧 ¡HTML con correos en {endpoint}!")
                            return response.text
                            
            except Exception as e:
                continue
                
        return None
        
    def contiene_datos_correo(self, data):
        """Verifica si los datos JSON contienen información de correos"""
        data_str = str(data).lower()
        indicadores = ['message', 'email', 'subject', 'from', 'to', 'sender', 'inbox', 'mail']
        return sum(1 for ind in indicadores if ind in data_str) >= 2
        
    def html_contiene_correos(self, html):
        """Verifica si el HTML contiene correos reales"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Buscar elementos que sugieran correos reales
        email_indicators = soup.find_all(text=lambda text: text and '@' in text and '.' in text)
        subject_indicators = soup.find_all(text=lambda text: text and 'subject:' in text.lower())
        
        return len(email_indicators) > 1 or len(subject_indicators) > 0
        
    def obtener_correos_completo(self):
        """Método principal para obtener correos"""
        print("📧 OBTENIENDO CORREOS - MÉTODO COMPLETO")
        print("=" * 60)
        
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None
            
        resultados = {
            'timestamp': datetime.now().isoformat(),
            'livewire_data': None,
            'ajax_data': None,
            'correos_encontrados': []
        }
        
        # 1. Extraer datos de Livewire
        print("\n1️⃣ Extrayendo datos de Livewire...")
        livewire_data = self.extraer_datos_livewire()
        
        if livewire_data:
            resultados['livewire_data'] = livewire_data
            
            # Probar llamadas a Livewire
            for wire_data in livewire_data:
                print(f"\n🔄 Probando Livewire: {wire_data['wire_id']}")
                
                # Método principal
                response = self.hacer_llamada_livewire(wire_data)
                if response:
                    resultados['correos_encontrados'].append({
                        'source': 'livewire_main',
                        'wire_id': wire_data['wire_id'],
                        'data': response
                    })
                    
                # Otros métodos
                response2 = self.probar_otros_metodos_livewire(wire_data)
                if response2:
                    resultados['correos_encontrados'].append({
                        'source': 'livewire_alt',
                        'wire_id': wire_data['wire_id'],
                        'data': response2
                    })
                    
        # 2. Buscar endpoints AJAX
        print("\n2️⃣ Buscando endpoints AJAX...")
        ajax_data = self.buscar_endpoints_ajax_adicionales()
        if ajax_data:
            resultados['ajax_data'] = ajax_data
            resultados['correos_encontrados'].append({
                'source': 'ajax',
                'data': ajax_data
            })
            
        return resultados
        
    def mostrar_resultados(self, resultados):
        """Muestra los resultados encontrados"""
        print(f"\n📧 RESULTADOS DE BÚSQUEDA")
        print("=" * 40)
        
        if not resultados:
            print("❌ No se obtuvieron resultados")
            return
            
        correos = resultados.get('correos_encontrados', [])
        print(f"📊 Fuentes de datos encontradas: {len(correos)}")
        
        for i, correo in enumerate(correos, 1):
            print(f"\n📂 Fuente {i}: {correo['source'].upper()}")
            print("-" * 30)
            
            if 'wire_id' in correo:
                print(f"   Wire ID: {correo['wire_id']}")
                
            data = correo['data']
            print(f"   Tipo de datos: {type(data).__name__}")
            print(f"   Contenido: {str(data)[:200]}...")
            
            # Intentar extraer información específica de correos
            if isinstance(data, dict):
                self.analizar_datos_dict(data)
            elif isinstance(data, str):
                self.analizar_datos_string(data)
                
        # Guardar resultados
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_livewire_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(resultados, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Resultados guardados en: {filename}")
        
    def analizar_datos_dict(self, data):
        """Analiza datos en formato diccionario"""
        # Buscar claves que sugieran correos
        claves_correo = ['messages', 'emails', 'inbox', 'mail', 'items']
        
        for clave in claves_correo:
            if clave in data:
                print(f"   📧 Clave encontrada: {clave} = {data[clave]}")
                
        # Buscar en effects (común en Livewire)
        if 'effects' in data:
            effects = data['effects']
            print(f"   🔄 Effects: {effects}")
            
        # Buscar en serverMemo
        if 'serverMemo' in data:
            server_memo = data['serverMemo']
            if 'data' in server_memo:
                print(f"   📊 ServerMemo data: {server_memo['data']}")
                
    def analizar_datos_string(self, data):
        """Analiza datos en formato string"""
        # Buscar patrones de email
        import re
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', data)
        if emails:
            print(f"   📧 Emails encontrados: {emails}")
            
        # Buscar patrones de subject
        subjects = re.findall(r'subject[:\s]*([^\n\r]+)', data, re.IGNORECASE)
        if subjects:
            print(f"   📝 Subjects encontrados: {subjects}")


def main():
    """Función principal"""
    print("🚀 OBTENER CORREOS VÍA LIVEWIRE")
    print("=" * 50)
    
    fetcher = LivewireEmailFetcher()
    
    try:
        resultados = fetcher.obtener_correos_completo()
        fetcher.mostrar_resultados(resultados)
        
        if resultados and resultados.get('correos_encontrados'):
            print(f"\n🎉 ¡Se encontraron {len(resultados['correos_encontrados'])} fuentes de datos!")
        else:
            print(f"\n😞 No se encontraron correos")
            print("💡 Los correos podrían requerir JavaScript del lado del cliente")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        fetcher.logout()
        
    print("\n🏁 Búsqueda completada")


if __name__ == "__main__":
    main()
