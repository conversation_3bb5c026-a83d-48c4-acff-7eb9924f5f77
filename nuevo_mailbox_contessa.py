#!/usr/bin/env python3
"""
Script para acceder al nuevo mailbox de Contessa y extraer correos
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


class ContessaMailboxReader(DujawAPI):
    def __init__(self):
        super().__init__()
        # Actualizar URLs para el nuevo mailbox
        self.mailbox_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.target_url = self.mailbox_url
        # Mantener la misma contraseña
        self.password = "unlockgs2024"
        
    def extraer_datos_livewire_del_html(self):
        """Extrae los datos exactos de Livewire del HTML"""
        print("🔍 Extrayendo datos de Livewire del nuevo mailbox...")
        
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return None
            
        html_content = response.text
        
        # Guardar HTML para debug
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_filename = f"contessa_mailbox_{timestamp}.html"
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 HTML guardado: {html_filename}")
        
        # Buscar el wire:initial-data del componente frontend.app
        app_pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"[^>]*'
        
        # Buscar todas las coincidencias y filtrar por frontend.app
        app_match = None
        for match in re.finditer(app_pattern, html_content):
            wire_id = match.group(1)
            initial_data_encoded = match.group(2)
            
            # Verificar si contiene frontend.app
            if 'frontend.app' in initial_data_encoded:
                app_match = match
                break
                
        if app_match:
            wire_id = app_match.group(1)
            initial_data_encoded = app_match.group(2)
            
            # Decodificar HTML entities
            import html
            initial_data_json = html.unescape(initial_data_encoded)
            
            try:
                initial_data = json.loads(initial_data_json)
                print(f"✅ Datos del componente frontend.app encontrados")
                print(f"   Wire ID: {wire_id}")
                print(f"   Email: {initial_data['serverMemo']['data']['email']}")
                print(f"   Messages: {len(initial_data['serverMemo']['data']['messages'])}")
                
                return {
                    'wire_id': wire_id,
                    'initial_data': initial_data,
                    'component_name': 'frontend.app'
                }
                
            except json.JSONDecodeError as e:
                print(f"❌ Error decodificando JSON: {e}")
                print(f"   JSON: {initial_data_json[:200]}...")
                
        else:
            print("❌ No se encontró el componente frontend.app")
            
        return None
        
    def hacer_llamadas_livewire(self, livewire_data):
        """Hace las llamadas de Livewire para obtener mensajes"""
        print("🔄 Haciendo llamadas Livewire...")
        
        wire_id = livewire_data['wire_id']
        initial_data = livewire_data['initial_data']
        
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.mailbox_url
        }
        
        livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
        
        resultados = []
        
        # 1. syncEmail
        print("🔄 Ejecutando syncEmail...")
        sync_request = {
            'fingerprint': initial_data['fingerprint'],
            'serverMemo': initial_data['serverMemo'],
            'updates': [
                {
                    'type': 'fireEvent',
                    'payload': {
                        'id': wire_id,
                        'event': 'syncEmail',
                        'params': [initial_data['serverMemo']['data']['email']]
                    }
                }
            ]
        }
        
        try:
            response = self.session.post(livewire_url, json=sync_request, headers=headers)
            if response.status_code == 200:
                print("✅ syncEmail exitoso")
                resultados.append(('syncEmail', response.json()))
        except Exception as e:
            print(f"❌ Error en syncEmail: {e}")
            
        # 2. fetchMessages (múltiples intentos)
        for intento in range(3):
            print(f"🔄 fetchMessages intento {intento + 1}/3...")
            
            fetch_request = {
                'fingerprint': initial_data['fingerprint'],
                'serverMemo': initial_data['serverMemo'],
                'updates': [
                    {
                        'type': 'fireEvent',
                        'payload': {
                            'id': wire_id,
                            'event': 'fetchMessages',
                            'params': []
                        }
                    }
                ]
            }
            
            try:
                response = self.session.post(livewire_url, json=fetch_request, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    resultados.append((f'fetchMessages_{intento+1}', data))
                    
                    # Verificar si hay mensajes
                    if self.tiene_mensajes(data):
                        print(f"🎉 ¡Mensajes encontrados en intento {intento + 1}!")
                        break
                        
            except Exception as e:
                print(f"❌ Error en fetchMessages {intento + 1}: {e}")
                
            import time
            time.sleep(2)
            
        return resultados
        
    def tiene_mensajes(self, response_data):
        """Verifica si la respuesta contiene mensajes"""
        if not isinstance(response_data, dict):
            return False
            
        # Buscar mensajes en serverMemo
        if 'serverMemo' in response_data and 'data' in response_data['serverMemo']:
            server_data = response_data['serverMemo']['data']
            if 'messages' in server_data and len(server_data['messages']) > 0:
                return True
                
        return False
        
    def extraer_links_de_aprobacion(self, resultados):
        """Extrae links de aprobación de los resultados"""
        print("🔗 Extrayendo links de aprobación...")
        
        links_encontrados = []
        
        for tipo, data in resultados:
            if isinstance(data, dict):
                # Buscar en serverMemo
                if 'serverMemo' in data and 'data' in data['serverMemo']:
                    server_data = data['serverMemo']['data']
                    if 'messages' in server_data:
                        for mensaje in server_data['messages']:
                            if isinstance(mensaje, dict) and 'body' in mensaje:
                                body = mensaje['body']
                                links = self.extraer_links_del_texto(body)
                                for link in links:
                                    links_encontrados.append({
                                        'source': tipo,
                                        'message': mensaje,
                                        'link': link
                                    })
                                    
                # Buscar en effects.html
                if 'effects' in data and 'html' in data['effects']:
                    html_content = data['effects']['html']
                    links = self.extraer_links_del_html(html_content)
                    for link in links:
                        links_encontrados.append({
                            'source': f'{tipo}_html',
                            'link': link
                        })
                        
        return links_encontrados
        
    def extraer_links_del_texto(self, texto):
        """Extrae links de aprobación del texto"""
        if not texto:
            return []
            
        # Patrón para links de aprobación de Pokémon
        pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
        links = re.findall(pattern, texto, re.IGNORECASE)
        
        return links
        
    def extraer_links_del_html(self, html_content):
        """Extrae links de aprobación del HTML"""
        if not html_content:
            return []
            
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        # Buscar en href
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            if 'email-change-approval' in href:
                links.append(href)
                
        # Buscar en texto
        text_content = soup.get_text()
        pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
        text_links = re.findall(pattern, text_content, re.IGNORECASE)
        links.extend(text_links)
        
        return list(set(links))  # Remover duplicados
        
    def procesar_mailbox_completo(self):
        """Procesa completamente el mailbox de Contessa"""
        print("📧 PROCESANDO MAILBOX DE CONTESSA")
        print("=" * 60)
        print(f"📧 Email: <EMAIL>")
        print(f"🔑 Password: {self.password}")
        print("=" * 60)
        
        # 1. Unlock
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None
            
        # 2. Extraer datos Livewire
        livewire_data = self.extraer_datos_livewire_del_html()
        if not livewire_data:
            print("❌ No se pudieron extraer datos de Livewire")
            return None
            
        # 3. Hacer llamadas Livewire
        resultados = self.hacer_llamadas_livewire(livewire_data)
        
        # 4. Extraer links de aprobación
        links = self.extraer_links_de_aprobacion(resultados)
        
        # 5. Mostrar resultados
        self.mostrar_resultados(resultados, links)
        
        # 6. Guardar todo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"contessa_correos_{timestamp}.json"
        
        data_completa = {
            'timestamp': datetime.now().isoformat(),
            'email': '<EMAIL>',
            'livewire_data': livewire_data,
            'resultados': resultados,
            'links_aprobacion': links
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data_completa, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Datos completos guardados en: {filename}")
        
        return data_completa
        
    def mostrar_resultados(self, resultados, links):
        """Muestra los resultados encontrados"""
        print(f"\n📧 RESULTADOS DEL MAILBOX CONTESSA")
        print("=" * 50)
        
        # Contar mensajes
        total_mensajes = 0
        for tipo, data in resultados:
            if isinstance(data, dict) and 'serverMemo' in data and 'data' in data['serverMemo']:
                server_data = data['serverMemo']['data']
                if 'messages' in server_data:
                    total_mensajes += len(server_data['messages'])
                    
        print(f"📊 Total de mensajes encontrados: {total_mensajes}")
        print(f"🔗 Links de aprobación encontrados: {len(links)}")
        
        # Mostrar links
        if links:
            print(f"\n🎯 LINKS DE APROBACIÓN:")
            print("-" * 40)
            
            for i, link_data in enumerate(links, 1):
                print(f"\n{i}. {link_data['link']}")
                print(f"   Fuente: {link_data['source']}")
                
                if 'message' in link_data:
                    mensaje = link_data['message']
                    if 'subject' in mensaje:
                        print(f"   Asunto: {mensaje['subject']}")
                    if 'from' in mensaje:
                        print(f"   De: {mensaje['from']}")
                    if 'date' in mensaje:
                        print(f"   Fecha: {mensaje['date']}")
        else:
            print("\n❌ No se encontraron links de aprobación")
            print("💡 Posibles razones:")
            print("   - No hay correos en el mailbox")
            print("   - Los correos aún se están cargando")
            print("   - No son correos de Pokémon")
            
        return links


def main():
    """Función principal"""
    print("🚀 LECTOR DE MAILBOX CONTESSA")
    print("=" * 50)
    
    reader = ContessaMailboxReader()
    
    try:
        data = reader.procesar_mailbox_completo()
        
        if data and data['links_aprobacion']:
            links = data['links_aprobacion']
            print(f"\n🎉 ¡Se encontraron {len(links)} links de aprobación!")
            
            # Preguntar si quiere seguir un link
            print(f"\n🔗 ¿Quieres seguir algún link ahora? (y/n)")
            respuesta = input().lower().strip()
            
            if respuesta == 'y' and links:
                from seguir_link_aprobacion import PokemonLinkFollower
                follower = PokemonLinkFollower()
                
                print(f"\n🔗 LINKS DISPONIBLES:")
                for i, link_data in enumerate(links, 1):
                    print(f"{i}. {link_data['link']}")
                    
                try:
                    num = int(input(f"\nSelecciona link (1-{len(links)}) [1]: ").strip() or "1")
                    if 1 <= num <= len(links):
                        link_seleccionado = links[num - 1]['link']
                        print(f"\n🔗 Siguiendo link seleccionado...")
                        follower.seguir_link(link_seleccionado)
                    else:
                        print("❌ Número inválido")
                except ValueError:
                    print("❌ Entrada inválida")
        else:
            print(f"\n😞 No se encontraron links de aprobación en este mailbox")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        reader.logout()
        
    print(f"\n🏁 Proceso completado")


if __name__ == "__main__":
    main()
