#!/usr/bin/env python3
"""
Script para extraer todos los links de los correos capturados
"""

import json
import re
from urllib.parse import urlparse


def extraer_links_de_correos():
    """Extrae todos los links de los correos"""
    print("🔗 EXTRAYENDO LINKS DE LOS CORREOS")
    print("=" * 50)
    
    # Cargar el archivo JSON más reciente con los correos
    filename = "correos_livewire_real_20250623_234515.json"
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        print(f"✅ Archivo cargado: {filename}")
        
        # Extraer el HTML de los correos
        fetch_responses = data.get('fetch_responses', [])
        
        all_links = []
        
        for response in fetch_responses:
            response_data = response.get('data', {})
            effects = response_data.get('effects', {})
            html_content = effects.get('html', '')
            
            if html_content:
                print(f"\n📧 Analizando HTML de respuesta...")
                links = extraer_links_del_html(html_content)
                all_links.extend(links)
                
        # Mostrar todos los links encontrados
        mostrar_links(all_links)
        
        # Guardar links en archivo
        guardar_links(all_links)
        
    except FileNotFoundError:
        print(f"❌ Archivo no encontrado: {filename}")
    except Exception as e:
        print(f"❌ Error: {e}")


def extraer_links_del_html(html_content):
    """Extrae links del contenido HTML"""
    links = []
    
    # Patrones para encontrar links
    patterns = [
        r'href=["\']([^"\']+)["\']',  # href="url"
        r'href=([^\s>]+)',            # href=url
        r'https?://[^\s<>"\']+',      # URLs directas
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        for match in matches:
            if match.startswith(('http://', 'https://')):
                links.append({
                    'url': match,
                    'type': 'direct_url',
                    'domain': urlparse(match).netloc
                })
            elif match.startswith('/'):
                # URL relativa
                full_url = f"https://pokemon.com{match}"
                links.append({
                    'url': full_url,
                    'type': 'relative_url',
                    'domain': urlparse(full_url).netloc,
                    'original': match
                })
            elif match.startswith('#'):
                # Anchor link
                links.append({
                    'url': match,
                    'type': 'anchor',
                    'domain': 'local'
                })
    
    # Buscar específicamente el link de aprobación de email
    approval_pattern = r'href=["\']([^"\']*email-change-approval[^"\']*)["\']'
    approval_matches = re.findall(approval_pattern, html_content, re.IGNORECASE)
    
    for match in approval_matches:
        links.append({
            'url': match,
            'type': 'email_approval',
            'domain': urlparse(match).netloc,
            'description': 'Link de aprobación de cambio de email'
        })
    
    # Buscar links de imágenes
    img_pattern = r'src=["\']([^"\']+)["\']'
    img_matches = re.findall(img_pattern, html_content, re.IGNORECASE)
    
    for match in img_matches:
        if match.startswith(('http://', 'https://')):
            links.append({
                'url': match,
                'type': 'image',
                'domain': urlparse(match).netloc
            })
    
    # Remover duplicados
    unique_links = []
    seen_urls = set()
    
    for link in links:
        url = link['url']
        if url not in seen_urls:
            seen_urls.add(url)
            unique_links.append(link)
    
    return unique_links


def mostrar_links(links):
    """Muestra los links organizados por tipo"""
    print(f"\n🔗 LINKS ENCONTRADOS: {len(links)}")
    print("=" * 60)
    
    if not links:
        print("❌ No se encontraron links")
        return
    
    # Agrupar por tipo
    por_tipo = {}
    for link in links:
        tipo = link['type']
        if tipo not in por_tipo:
            por_tipo[tipo] = []
        por_tipo[tipo].append(link)
    
    # Mostrar links importantes primero
    tipos_importantes = ['email_approval', 'direct_url', 'relative_url']
    
    for tipo in tipos_importantes:
        if tipo in por_tipo:
            links_tipo = por_tipo[tipo]
            print(f"\n📂 {tipo.upper().replace('_', ' ')} ({len(links_tipo)} links):")
            print("-" * 50)
            
            for i, link in enumerate(links_tipo, 1):
                print(f"\n  {i}. {link['url']}")
                print(f"     Dominio: {link['domain']}")
                
                if 'description' in link:
                    print(f"     Descripción: {link['description']}")
                    
                if 'original' in link:
                    print(f"     Original: {link['original']}")
    
    # Mostrar otros tipos
    otros_tipos = [tipo for tipo in por_tipo.keys() if tipo not in tipos_importantes]
    
    for tipo in otros_tipos:
        links_tipo = por_tipo[tipo]
        print(f"\n📂 {tipo.upper().replace('_', ' ')} ({len(links_tipo)} links):")
        print("-" * 30)
        
        # Solo mostrar los primeros 5 para no saturar
        for i, link in enumerate(links_tipo[:5], 1):
            print(f"  {i}. {link['url']}")
            
        if len(links_tipo) > 5:
            print(f"  ... y {len(links_tipo) - 5} más")
    
    # Mostrar resumen de dominios
    print(f"\n🌐 DOMINIOS ENCONTRADOS:")
    print("-" * 30)
    
    dominios = {}
    for link in links:
        domain = link['domain']
        if domain != 'local':
            dominios[domain] = dominios.get(domain, 0) + 1
    
    for domain, count in sorted(dominios.items(), key=lambda x: x[1], reverse=True):
        print(f"  {domain}: {count} links")


def guardar_links(links):
    """Guarda los links en un archivo JSON"""
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"links_extraidos_{timestamp}.json"
    
    # Preparar datos para guardar
    data_to_save = {
        'timestamp': datetime.now().isoformat(),
        'total_links': len(links),
        'links': links
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data_to_save, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Links guardados en: {filename}")


def mostrar_links_importantes():
    """Muestra solo los links más importantes"""
    print(f"\n⭐ LINKS MÁS IMPORTANTES:")
    print("=" * 40)
    
    # Estos son los links que probablemente te interesan más
    links_importantes = [
        "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/bb026d3abb3157d38a86d2ebf4be1e26",
        "http://support.pokemon.com",
        "http://pokemon.com",
        "https://corporate.pokemon.com/en-us/",
        "http://www.pokemon.com/us/terms-of-use/",
        "http://www.pokemon.com/us/privacy-notice/",
        "http://www.pokemon.com/us/cookie-page/"
    ]
    
    for i, link in enumerate(links_importantes, 1):
        print(f"\n{i}. {link}")
        
        if 'email-change-approval' in link:
            print("   🎯 LINK PRINCIPAL - Aprobación de cambio de email")
            print("   ⚠️ Este link expira en 24 horas")
        elif 'support.pokemon.com' in link:
            print("   📞 Soporte técnico de Pokémon")
        elif link == "http://pokemon.com":
            print("   🏠 Sitio web principal de Pokémon")
        elif 'corporate.pokemon.com' in link:
            print("   🏢 Información corporativa")
        elif 'terms-of-use' in link:
            print("   📜 Términos de uso")
        elif 'privacy-notice' in link:
            print("   🔒 Política de privacidad")
        elif 'cookie-page' in link:
            print("   🍪 Política de cookies")


def main():
    """Función principal"""
    print("🔗 EXTRACTOR DE LINKS DE CORREOS")
    print("=" * 50)
    
    # Extraer todos los links
    extraer_links_de_correos()
    
    # Mostrar links importantes
    mostrar_links_importantes()
    
    print(f"\n🏁 Extracción de links completada")


if __name__ == "__main__":
    main()
