#!/usr/bin/env python3
"""
Ejemplo de uso práctico de la API de Dujaw
Demuestra cómo automatizar diferentes tareas del mailbox
"""

import time
import json
from datetime import datetime
from dujaw_api_final import DujawAPI


def monitorear_mailbox(api: DujawAPI, intervalo_segundos: int = 30, max_iteraciones: int = 10):
    """
    Monitorea el mailbox por nuevos mensajes
    
    Args:
        api: Instancia de DujawAPI
        intervalo_segundos: Tiempo entre verificaciones
        max_iteraciones: Máximo número de verificaciones
    """
    print(f"📧 Iniciando monitoreo del mailbox (cada {intervalo_segundos}s, máx {max_iteraciones} veces)")
    
    for i in range(max_iteraciones):
        print(f"\n🔍 Verificación {i+1}/{max_iteraciones} - {datetime.now().strftime('%H:%M:%S')}")
        
        # Obtener estado del mailbox
        status = api.get_mailbox_status()
        
        if status['status'] == 'active':
            message_count = status['message_count']
            print(f"📊 Estado: {message_count} mensajes")
            
            if message_count > 0:
                print("🎉 ¡Nuevos mensajes encontrados!")
                # Aquí podrías procesar los mensajes
                break
            else:
                print("📭 Sin mensajes nuevos")
        else:
            print(f"❌ Error en mailbox: {status.get('error', 'Desconocido')}")
            
        # Esperar antes de la siguiente verificación
        if i < max_iteraciones - 1:
            print(f"⏳ Esperando {intervalo_segundos} segundos...")
            time.sleep(intervalo_segundos)
            
    print("🏁 Monitoreo completado")


def obtener_informacion_completa(api: DujawAPI):
    """
    Obtiene información completa del mailbox
    """
    print("📋 Obteniendo información completa del mailbox...")
    
    # Acceder al mailbox
    mailbox_info = api.access_mailbox()
    
    if mailbox_info:
        print("\n📊 INFORMACIÓN DEL MAILBOX:")
        print("=" * 40)
        print(f"📧 Email: {mailbox_info.get('email_address', 'N/A')}")
        print(f"📅 Timestamp: {mailbox_info.get('timestamp', 'N/A')}")
        print(f"📨 Mensajes: {len(mailbox_info.get('messages', []))}")
        print(f"🔧 Acciones: {len(mailbox_info.get('actions', []))}")
        
        # Mostrar mensajes si los hay
        messages = mailbox_info.get('messages', [])
        if messages:
            print(f"\n📨 MENSAJES ({len(messages)}):")
            for i, msg in enumerate(messages, 1):
                print(f"  {i}. {msg.get('content', 'Sin contenido')[:100]}...")
        else:
            print("\n📭 No hay mensajes")
            
        # Mostrar acciones disponibles
        actions = mailbox_info.get('actions', [])
        if actions:
            print(f"\n🔧 ACCIONES DISPONIBLES ({len(actions)}):")
            for i, action in enumerate(actions, 1):
                print(f"  {i}. {action.get('text', 'Sin texto')} ({action.get('type', 'N/A')})")
                if action.get('href'):
                    print(f"     URL: {action['href']}")
        else:
            print("\n🔧 No hay acciones disponibles")
            
        return mailbox_info
    else:
        print("❌ Error obteniendo información del mailbox")
        return None


def verificar_conectividad(api: DujawAPI):
    """
    Verifica la conectividad y autenticación
    """
    print("🔍 Verificando conectividad...")
    
    # Intentar obtener token CSRF
    token = api.get_csrf_token()
    if token:
        print("✅ Conectividad OK - Token CSRF obtenido")
        
        # Intentar unlock
        if api.unlock_mailbox():
            print("✅ Autenticación OK - Unlock exitoso")
            
            # Verificar acceso al mailbox
            status = api.get_mailbox_status()
            if status['status'] == 'active':
                print("✅ Acceso al mailbox OK")
                return True
            else:
                print("❌ Error accediendo al mailbox")
                return False
        else:
            print("❌ Error en autenticación")
            return False
    else:
        print("❌ Error de conectividad")
        return False


def guardar_estado_mailbox(api: DujawAPI, archivo: str = None):
    """
    Guarda el estado actual del mailbox en un archivo JSON
    """
    if not archivo:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        archivo = f"mailbox_estado_{timestamp}.json"
        
    print(f"💾 Guardando estado del mailbox en: {archivo}")
    
    # Obtener información completa
    mailbox_info = api.access_mailbox()
    status = api.get_mailbox_status()
    
    estado_completo = {
        'timestamp': datetime.now().isoformat(),
        'mailbox_info': mailbox_info,
        'status': status,
        'email': '<EMAIL>'
    }
    
    try:
        with open(archivo, 'w', encoding='utf-8') as f:
            json.dump(estado_completo, f, indent=2, ensure_ascii=False)
        print(f"✅ Estado guardado en: {archivo}")
        return archivo
    except Exception as e:
        print(f"❌ Error guardando estado: {e}")
        return None


def main():
    """
    Función principal con ejemplos de uso
    """
    print("🚀 EJEMPLOS DE USO - DUJAW API")
    print("=" * 50)
    
    # Crear instancia de la API
    api = DujawAPI()
    
    try:
        print("\n1️⃣ VERIFICANDO CONECTIVIDAD...")
        if verificar_conectividad(api):
            print("✅ Sistema listo para usar")
            
            print("\n2️⃣ OBTENIENDO INFORMACIÓN COMPLETA...")
            obtener_informacion_completa(api)
            
            print("\n3️⃣ GUARDANDO ESTADO ACTUAL...")
            archivo_estado = guardar_estado_mailbox(api)
            
            print("\n4️⃣ MONITOREO DE MENSAJES...")
            print("¿Quieres monitorear por nuevos mensajes? (y/n)")
            respuesta = input().lower().strip()
            
            if respuesta == 'y':
                print("Ingresa intervalo en segundos [30]:")
                try:
                    intervalo = int(input().strip() or "30")
                except:
                    intervalo = 30
                    
                print("Ingresa máximo de verificaciones [5]:")
                try:
                    max_iter = int(input().strip() or "5")
                except:
                    max_iter = 5
                    
                monitorear_mailbox(api, intervalo, max_iter)
            else:
                print("⏭️ Saltando monitoreo")
                
            print("\n5️⃣ ESTADO FINAL...")
            status_final = api.get_mailbox_status()
            print(f"📊 Estado final: {json.dumps(status_final, indent=2)}")
            
        else:
            print("❌ Error de conectividad - no se puede continuar")
            
    except KeyboardInterrupt:
        print("\n⏹️ Interrumpido por el usuario")
        
    except Exception as e:
        print(f"\n❌ Error general: {e}")
        
    finally:
        # Cerrar sesión
        api.logout()
        print("\n🏁 Ejemplos completados")


if __name__ == "__main__":
    main()
