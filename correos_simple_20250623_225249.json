[{"tipo": "script_json", "intento": 1, "contenido": "{'Get back to MailBox': 'Get back to MailBox', 'Enter Username': 'Enter Username', 'Select Domain': 'Select Domain', 'Create': 'Create', 'Random': 'Random', 'Custom': 'Custom', 'Menu': 'Menu', 'Cancel': 'Cancel', 'Copy': 'Copy', 'Refresh': 'Refresh', 'New': 'New', 'Delete': 'Delete', 'Download': 'Download', 'Fetching': 'Fetching', 'Empty Inbox': 'Empty Inbox', 'Error': 'Error', 'Success': 'Success', 'Close': 'Close', 'Email ID Copied to Clipboard': 'Email ID Copied to Clipboard', 'Please enter Username': 'Please enter Username', 'Please Select a Domain': 'Please Select a Domain', 'Username not allowed': 'Username not allowed', 'Your Temporary Email Address': 'Your Temporary Email Address', 'Attachments': 'Attachments', 'Blocked': 'Blocked', 'Emails from': 'Emails from', 'are blocked by Admin': 'are blocked by Admin', 'No Messages': 'No Messages', 'Waiting for Incoming Messages': 'Waiting for Incoming Messages', 'Scan QR Code to access': 'Scan QR Code to access', 'Create your own Temp Mail': 'Create your own Temp Mail', 'Your Temprorary Email': 'Your Temprorary Email', 'Enter a Username and Select the Domain': 'Enter a Username and Select the Domain', 'Username length cannot be less than': 'Username length cannot be less than', 'and greator than': 'and greator than', 'Create a Random Email': 'Create a Random Email', 'Sender': 'Sender', 'Subject': 'Subject', 'Time': 'Time', 'Open': 'Open', 'Go Back to Inbox': 'Go Back to Inbox', 'Date': 'Date', 'Copyright': 'Copyright', 'Ad Blocker Detected': 'Ad Blocker Detected', 'Disable the Ad Blocker to use ': 'Disable the Ad Blocker to use ', 'Your temporary email address is ready': 'Your temporary email address is ready', 'You have reached daily limit of MAX ': 'You have reached daily limit of MAX ', ' temp mail': ' temp mail', 'Sorry! That email is already been used by someone else. Please try a different email address.': 'Sorry! That email is already been used by someone else. Please try a different email address.', 'Invalid Captcha. Please try again': 'Invalid Captcha. Please try again', 'Invalid Password': 'Invalid Password', 'Password': 'Password', 'Unlock': 'Unlock', 'Your Name': 'Your Name', 'Enter your Name': 'Enter your Name', 'Your Email': 'Your Email', 'Enter your Email': 'Enter your Email', 'Message': 'Message', 'Enter your Message': 'Enter your Message', 'Send Message': 'Send Message'}", "data": {"Get back to MailBox": "Get back to MailBox", "Enter Username": "<PERSON><PERSON> Username", "Select Domain": "Select Domain", "Create": "Create", "Random": "Random", "Custom": "Custom", "Menu": "<PERSON><PERSON>", "Cancel": "Cancel", "Copy": "Copy", "Refresh": "Refresh", "New": "New", "Delete": "Delete", "Download": "Download", "Fetching": "Fetching", "Empty Inbox": "Empty Inbox", "Error": "Error", "Success": "Success", "Close": "Close", "Email ID Copied to Clipboard": "Email ID Copied to Clipboard", "Please enter Username": "Please enter Username", "Please Select a Domain": "Please Select a Domain", "Username not allowed": "Username not allowed", "Your Temporary Email Address": "Your Temporary Email Address", "Attachments": "Attachments", "Blocked": "Blocked", "Emails from": "Emails from", "are blocked by Admin": "are blocked by Admin", "No Messages": "No Messages", "Waiting for Incoming Messages": "Waiting for Incoming Messages", "Scan QR Code to access": "Scan QR Code to access", "Create your own Temp Mail": "Create your own Temp Mail", "Your Temprorary Email": "Your Temprorary Email", "Enter a Username and Select the Domain": "Enter a Username and Select the Domain", "Username length cannot be less than": "Username length cannot be less than", "and greator than": "and greator than", "Create a Random Email": "Create a Random Email", "Sender": "Sender", "Subject": "Subject", "Time": "Time", "Open": "Open", "Go Back to Inbox": "Go Back to Inbox", "Date": "Date", "Copyright": "Copyright", "Ad Blocker Detected": "Ad Blocker Detected", "Disable the Ad Blocker to use ": "Disable the Ad Blocker to use ", "Your temporary email address is ready": "Your temporary email address is ready", "You have reached daily limit of MAX ": "You have reached daily limit of MAX ", " temp mail": " temp mail", "Sorry! That email is already been used by someone else. Please try a different email address.": "Sorry! That email is already been used by someone else. Please try a different email address.", "Invalid Captcha. Please try again": "Invalid <PERSON>. Please try again", "Invalid Password": "Invalid Password", "Password": "Password", "Unlock": "Unlock", "Your Name": "Your Name", "Enter your Name": "Enter your Name", "Your Email": "Your Email", "Enter your Email": "Enter your Email", "Message": "Message", "Enter your Message": "Enter your Message", "Send Message": "Send Message"}}, {"tipo": "div_content", "intento": 1, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>...", "html": "<div class=\"default-theme\">\n<div class=\"flex flex-wrap\">\n<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/imag"}, {"tipo": "div_content", "intento": 1, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>...", "html": "<div class=\"flex flex-wrap\">\n<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\"/>\n</a>\n</di"}, {"tipo": "div_content", "intento": 1, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>", "html": "<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\"/>\n</a>\n</div>\n<div wire:id=\"plZey7KfeOaL"}, {"tipo": "div_content", "intento": 1, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>", "html": "<div wire:id=\"plZey7KfeOaLFYGo3wXZ\" wire:initial-data='{\"fingerprint\":{\"id\":\"plZey7KfeOaLFYGo3wXZ\",\"name\":\"frontend.actions\",\"locale\":\"en\",\"path\":\"mailbox\",\"method\":\"GET\",\"v\":\"acj\"},\"effects\":{\"listeners\":[\"syncEmail\",\"checkReCaptcha3\"]},\"serverMemo\":{\"children\":[],\"errors\":[],\"htmlHash\":\"f397053a\","}, {"tipo": "div_content", "intento": 1, "contenido": "constance30904@<EMAIL>", "html": "<div class=\"in-app-actions mt-4 px-8\" style=\"display: none;\" x-show.transition.in=\"!in_app\">\n<form action=\"#\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n<div class=\"relative\">\n<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open ="}, {"tipo": "div_content", "intento": 1, "contenido": "constance30904@<EMAIL>", "html": "<div class=\"relative\">\n<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none"}, {"tipo": "div_content", "intento": 1, "contenido": "constance30904@<EMAIL>", "html": "<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\">constan"}, {"tipo": "div_content", "intento": 1, "contenido": "<EMAIL>", "html": "<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n</div>"}, {"tipo": "div_content", "intento": 1, "contenido": "<EMAIL>", "html": "<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>"}, {"tipo": "div_content", "intento": 1, "contenido": "<EMAIL>", "html": "<div @click=\"open = false\" class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\" style=\"display: none;\" x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-t"}, {"tipo": "div_content", "intento": 1, "contenido": "<EMAIL>", "html": "<div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n<a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\">constance309"}, {"tipo": "div_content", "intento": 1, "contenido": "ErrorSuccessEmail ID Copied to Clipboard", "html": "<div class=\"hidden language-helper\">\n<div class=\"error\">Error</div>\n<div class=\"success\">Success</div>\n<div class=\"copy_text\">Email ID Copied to Clipboard</div>\n</div>"}, {"tipo": "div_content", "intento": 1, "contenido": "Email ID Copied to Clipboard", "html": "<div class=\"copy_text\">Email ID Copied to Clipboard</div>"}, {"tipo": "script_json", "intento": 2, "contenido": "{'Get back to MailBox': 'Get back to MailBox', 'Enter Username': 'Enter Username', 'Select Domain': 'Select Domain', 'Create': 'Create', 'Random': 'Random', 'Custom': 'Custom', 'Menu': 'Menu', 'Cancel': 'Cancel', 'Copy': 'Copy', 'Refresh': 'Refresh', 'New': 'New', 'Delete': 'Delete', 'Download': 'Download', 'Fetching': 'Fetching', 'Empty Inbox': 'Empty Inbox', 'Error': 'Error', 'Success': 'Success', 'Close': 'Close', 'Email ID Copied to Clipboard': 'Email ID Copied to Clipboard', 'Please enter Username': 'Please enter Username', 'Please Select a Domain': 'Please Select a Domain', 'Username not allowed': 'Username not allowed', 'Your Temporary Email Address': 'Your Temporary Email Address', 'Attachments': 'Attachments', 'Blocked': 'Blocked', 'Emails from': 'Emails from', 'are blocked by Admin': 'are blocked by Admin', 'No Messages': 'No Messages', 'Waiting for Incoming Messages': 'Waiting for Incoming Messages', 'Scan QR Code to access': 'Scan QR Code to access', 'Create your own Temp Mail': 'Create your own Temp Mail', 'Your Temprorary Email': 'Your Temprorary Email', 'Enter a Username and Select the Domain': 'Enter a Username and Select the Domain', 'Username length cannot be less than': 'Username length cannot be less than', 'and greator than': 'and greator than', 'Create a Random Email': 'Create a Random Email', 'Sender': 'Sender', 'Subject': 'Subject', 'Time': 'Time', 'Open': 'Open', 'Go Back to Inbox': 'Go Back to Inbox', 'Date': 'Date', 'Copyright': 'Copyright', 'Ad Blocker Detected': 'Ad Blocker Detected', 'Disable the Ad Blocker to use ': 'Disable the Ad Blocker to use ', 'Your temporary email address is ready': 'Your temporary email address is ready', 'You have reached daily limit of MAX ': 'You have reached daily limit of MAX ', ' temp mail': ' temp mail', 'Sorry! That email is already been used by someone else. Please try a different email address.': 'Sorry! That email is already been used by someone else. Please try a different email address.', 'Invalid Captcha. Please try again': 'Invalid Captcha. Please try again', 'Invalid Password': 'Invalid Password', 'Password': 'Password', 'Unlock': 'Unlock', 'Your Name': 'Your Name', 'Enter your Name': 'Enter your Name', 'Your Email': 'Your Email', 'Enter your Email': 'Enter your Email', 'Message': 'Message', 'Enter your Message': 'Enter your Message', 'Send Message': 'Send Message'}", "data": {"Get back to MailBox": "Get back to MailBox", "Enter Username": "<PERSON><PERSON> Username", "Select Domain": "Select Domain", "Create": "Create", "Random": "Random", "Custom": "Custom", "Menu": "<PERSON><PERSON>", "Cancel": "Cancel", "Copy": "Copy", "Refresh": "Refresh", "New": "New", "Delete": "Delete", "Download": "Download", "Fetching": "Fetching", "Empty Inbox": "Empty Inbox", "Error": "Error", "Success": "Success", "Close": "Close", "Email ID Copied to Clipboard": "Email ID Copied to Clipboard", "Please enter Username": "Please enter Username", "Please Select a Domain": "Please Select a Domain", "Username not allowed": "Username not allowed", "Your Temporary Email Address": "Your Temporary Email Address", "Attachments": "Attachments", "Blocked": "Blocked", "Emails from": "Emails from", "are blocked by Admin": "are blocked by Admin", "No Messages": "No Messages", "Waiting for Incoming Messages": "Waiting for Incoming Messages", "Scan QR Code to access": "Scan QR Code to access", "Create your own Temp Mail": "Create your own Temp Mail", "Your Temprorary Email": "Your Temprorary Email", "Enter a Username and Select the Domain": "Enter a Username and Select the Domain", "Username length cannot be less than": "Username length cannot be less than", "and greator than": "and greator than", "Create a Random Email": "Create a Random Email", "Sender": "Sender", "Subject": "Subject", "Time": "Time", "Open": "Open", "Go Back to Inbox": "Go Back to Inbox", "Date": "Date", "Copyright": "Copyright", "Ad Blocker Detected": "Ad Blocker Detected", "Disable the Ad Blocker to use ": "Disable the Ad Blocker to use ", "Your temporary email address is ready": "Your temporary email address is ready", "You have reached daily limit of MAX ": "You have reached daily limit of MAX ", " temp mail": " temp mail", "Sorry! That email is already been used by someone else. Please try a different email address.": "Sorry! That email is already been used by someone else. Please try a different email address.", "Invalid Captcha. Please try again": "Invalid <PERSON>. Please try again", "Invalid Password": "Invalid Password", "Password": "Password", "Unlock": "Unlock", "Your Name": "Your Name", "Enter your Name": "Enter your Name", "Your Email": "Your Email", "Enter your Email": "Enter your Email", "Message": "Message", "Enter your Message": "Enter your Message", "Send Message": "Send Message"}}, {"tipo": "div_content", "intento": 2, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>...", "html": "<div class=\"default-theme\">\n<div class=\"flex flex-wrap\">\n<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/imag"}, {"tipo": "div_content", "intento": 2, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>...", "html": "<div class=\"flex flex-wrap\">\n<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\"/>\n</a>\n</di"}, {"tipo": "div_content", "intento": 2, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>", "html": "<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\"/>\n</a>\n</div>\n<div wire:id=\"GqD9wTNe3gAb"}, {"tipo": "div_content", "intento": 2, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>", "html": "<div wire:id=\"GqD9wTNe3gAblZUEk7WR\" wire:initial-data='{\"fingerprint\":{\"id\":\"GqD9wTNe3gAblZUEk7WR\",\"name\":\"frontend.actions\",\"locale\":\"en\",\"path\":\"mailbox\",\"method\":\"GET\",\"v\":\"acj\"},\"effects\":{\"listeners\":[\"syncEmail\",\"checkReCaptcha3\"]},\"serverMemo\":{\"children\":[],\"errors\":[],\"htmlHash\":\"f397053a\","}, {"tipo": "div_content", "intento": 2, "contenido": "constance30904@<EMAIL>", "html": "<div class=\"in-app-actions mt-4 px-8\" style=\"display: none;\" x-show.transition.in=\"!in_app\">\n<form action=\"#\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n<div class=\"relative\">\n<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open ="}, {"tipo": "div_content", "intento": 2, "contenido": "constance30904@<EMAIL>", "html": "<div class=\"relative\">\n<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none"}, {"tipo": "div_content", "intento": 2, "contenido": "constance30904@<EMAIL>", "html": "<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\">constan"}, {"tipo": "div_content", "intento": 2, "contenido": "<EMAIL>", "html": "<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n</div>"}, {"tipo": "div_content", "intento": 2, "contenido": "<EMAIL>", "html": "<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>"}, {"tipo": "div_content", "intento": 2, "contenido": "<EMAIL>", "html": "<div @click=\"open = false\" class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\" style=\"display: none;\" x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-t"}, {"tipo": "div_content", "intento": 2, "contenido": "<EMAIL>", "html": "<div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n<a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\">constance309"}, {"tipo": "div_content", "intento": 2, "contenido": "ErrorSuccessEmail ID Copied to Clipboard", "html": "<div class=\"hidden language-helper\">\n<div class=\"error\">Error</div>\n<div class=\"success\">Success</div>\n<div class=\"copy_text\">Email ID Copied to Clipboard</div>\n</div>"}, {"tipo": "div_content", "intento": 2, "contenido": "Email ID Copied to Clipboard", "html": "<div class=\"copy_text\">Email ID Copied to Clipboard</div>"}, {"tipo": "script_json", "intento": 3, "contenido": "{'Get back to MailBox': 'Get back to MailBox', 'Enter Username': 'Enter Username', 'Select Domain': 'Select Domain', 'Create': 'Create', 'Random': 'Random', 'Custom': 'Custom', 'Menu': 'Menu', 'Cancel': 'Cancel', 'Copy': 'Copy', 'Refresh': 'Refresh', 'New': 'New', 'Delete': 'Delete', 'Download': 'Download', 'Fetching': 'Fetching', 'Empty Inbox': 'Empty Inbox', 'Error': 'Error', 'Success': 'Success', 'Close': 'Close', 'Email ID Copied to Clipboard': 'Email ID Copied to Clipboard', 'Please enter Username': 'Please enter Username', 'Please Select a Domain': 'Please Select a Domain', 'Username not allowed': 'Username not allowed', 'Your Temporary Email Address': 'Your Temporary Email Address', 'Attachments': 'Attachments', 'Blocked': 'Blocked', 'Emails from': 'Emails from', 'are blocked by Admin': 'are blocked by Admin', 'No Messages': 'No Messages', 'Waiting for Incoming Messages': 'Waiting for Incoming Messages', 'Scan QR Code to access': 'Scan QR Code to access', 'Create your own Temp Mail': 'Create your own Temp Mail', 'Your Temprorary Email': 'Your Temprorary Email', 'Enter a Username and Select the Domain': 'Enter a Username and Select the Domain', 'Username length cannot be less than': 'Username length cannot be less than', 'and greator than': 'and greator than', 'Create a Random Email': 'Create a Random Email', 'Sender': 'Sender', 'Subject': 'Subject', 'Time': 'Time', 'Open': 'Open', 'Go Back to Inbox': 'Go Back to Inbox', 'Date': 'Date', 'Copyright': 'Copyright', 'Ad Blocker Detected': 'Ad Blocker Detected', 'Disable the Ad Blocker to use ': 'Disable the Ad Blocker to use ', 'Your temporary email address is ready': 'Your temporary email address is ready', 'You have reached daily limit of MAX ': 'You have reached daily limit of MAX ', ' temp mail': ' temp mail', 'Sorry! That email is already been used by someone else. Please try a different email address.': 'Sorry! That email is already been used by someone else. Please try a different email address.', 'Invalid Captcha. Please try again': 'Invalid Captcha. Please try again', 'Invalid Password': 'Invalid Password', 'Password': 'Password', 'Unlock': 'Unlock', 'Your Name': 'Your Name', 'Enter your Name': 'Enter your Name', 'Your Email': 'Your Email', 'Enter your Email': 'Enter your Email', 'Message': 'Message', 'Enter your Message': 'Enter your Message', 'Send Message': 'Send Message'}", "data": {"Get back to MailBox": "Get back to MailBox", "Enter Username": "<PERSON><PERSON> Username", "Select Domain": "Select Domain", "Create": "Create", "Random": "Random", "Custom": "Custom", "Menu": "<PERSON><PERSON>", "Cancel": "Cancel", "Copy": "Copy", "Refresh": "Refresh", "New": "New", "Delete": "Delete", "Download": "Download", "Fetching": "Fetching", "Empty Inbox": "Empty Inbox", "Error": "Error", "Success": "Success", "Close": "Close", "Email ID Copied to Clipboard": "Email ID Copied to Clipboard", "Please enter Username": "Please enter Username", "Please Select a Domain": "Please Select a Domain", "Username not allowed": "Username not allowed", "Your Temporary Email Address": "Your Temporary Email Address", "Attachments": "Attachments", "Blocked": "Blocked", "Emails from": "Emails from", "are blocked by Admin": "are blocked by Admin", "No Messages": "No Messages", "Waiting for Incoming Messages": "Waiting for Incoming Messages", "Scan QR Code to access": "Scan QR Code to access", "Create your own Temp Mail": "Create your own Temp Mail", "Your Temprorary Email": "Your Temprorary Email", "Enter a Username and Select the Domain": "Enter a Username and Select the Domain", "Username length cannot be less than": "Username length cannot be less than", "and greator than": "and greator than", "Create a Random Email": "Create a Random Email", "Sender": "Sender", "Subject": "Subject", "Time": "Time", "Open": "Open", "Go Back to Inbox": "Go Back to Inbox", "Date": "Date", "Copyright": "Copyright", "Ad Blocker Detected": "Ad Blocker Detected", "Disable the Ad Blocker to use ": "Disable the Ad Blocker to use ", "Your temporary email address is ready": "Your temporary email address is ready", "You have reached daily limit of MAX ": "You have reached daily limit of MAX ", " temp mail": " temp mail", "Sorry! That email is already been used by someone else. Please try a different email address.": "Sorry! That email is already been used by someone else. Please try a different email address.", "Invalid Captcha. Please try again": "Invalid <PERSON>. Please try again", "Invalid Password": "Invalid Password", "Password": "Password", "Unlock": "Unlock", "Your Name": "Your Name", "Enter your Name": "Enter your Name", "Your Email": "Your Email", "Enter your Email": "Enter your Email", "Message": "Message", "Enter your Message": "Enter your Message", "Send Message": "Send Message"}}, {"tipo": "div_content", "intento": 3, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>...", "html": "<div class=\"default-theme\">\n<div class=\"flex flex-wrap\">\n<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/imag"}, {"tipo": "div_content", "intento": 3, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>...", "html": "<div class=\"flex flex-wrap\">\n<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\"/>\n</a>\n</di"}, {"tipo": "div_content", "intento": 3, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>", "html": "<div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n<div class=\"flex justify-center p-3 mb-10\">\n<a href=\"https://dujaw.com\">\n<img alt=\"logo\" class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\"/>\n</a>\n</div>\n<div wire:id=\"IYGvl2gAyQfj"}, {"tipo": "div_content", "intento": 3, "contenido": "dujaw.comfgeta.comdennisgls26.comaipicz.comgamersparky26.comwithsd.comzzetu.comdxgamers.comulnik.comrdmail.infoziuwi.comtseru.comgohuki.com1em0nstore.win1em0nstore.trade1emonstore.tradeCancelconstance30904@<EMAIL>", "html": "<div wire:id=\"IYGvl2gAyQfjQIFZPLYn\" wire:initial-data='{\"fingerprint\":{\"id\":\"IYGvl2gAyQfjQIFZPLYn\",\"name\":\"frontend.actions\",\"locale\":\"en\",\"path\":\"mailbox\",\"method\":\"GET\",\"v\":\"acj\"},\"effects\":{\"listeners\":[\"syncEmail\",\"checkReCaptcha3\"]},\"serverMemo\":{\"children\":[],\"errors\":[],\"htmlHash\":\"f397053a\","}, {"tipo": "div_content", "intento": 3, "contenido": "constance30904@<EMAIL>", "html": "<div class=\"in-app-actions mt-4 px-8\" style=\"display: none;\" x-show.transition.in=\"!in_app\">\n<form action=\"#\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n<div class=\"relative\">\n<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open ="}, {"tipo": "div_content", "intento": 3, "contenido": "constance30904@<EMAIL>", "html": "<div class=\"relative\">\n<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none"}, {"tipo": "div_content", "intento": 3, "contenido": "constance30904@<EMAIL>", "html": "<div @click.away=\"open = false\" @close.stop=\"open = false\" class=\"relative\" x-data=\"{ open: false }\">\n<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\">constan"}, {"tipo": "div_content", "intento": 3, "contenido": "<EMAIL>", "html": "<div @click=\"open = ! open\">\n<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n</div>"}, {"tipo": "div_content", "intento": 3, "contenido": "<EMAIL>", "html": "<div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>"}, {"tipo": "div_content", "intento": 3, "contenido": "<EMAIL>", "html": "<div @click=\"open = false\" class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\" style=\"display: none;\" x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-t"}, {"tipo": "div_content", "intento": 3, "contenido": "<EMAIL>", "html": "<div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n<a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\">constance309"}, {"tipo": "div_content", "intento": 3, "contenido": "ErrorSuccessEmail ID Copied to Clipboard", "html": "<div class=\"hidden language-helper\">\n<div class=\"error\">Error</div>\n<div class=\"success\">Success</div>\n<div class=\"copy_text\">Email ID Copied to Clipboard</div>\n</div>"}, {"tipo": "div_content", "intento": 3, "contenido": "Email ID Copied to Clipboard", "html": "<div class=\"copy_text\">Email ID Copied to Clipboard</div>"}]