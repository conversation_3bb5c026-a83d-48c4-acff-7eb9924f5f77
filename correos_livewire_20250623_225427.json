{"timestamp": "2025-06-23T22:54:20.662140", "livewire_data": [{"wire_id": "6XgJsXvp7aEW32c1i7Iv", "initial_data": {"fingerprint": {"id": "6XgJsXvp7aEW32c1i7Iv", "name": "frontend.actions", "locale": "en", "path": "mailbox", "method": "GET", "v": "acj"}, "effects": {"listeners": ["syncEmail", "checkReCaptcha3"]}, "serverMemo": {"children": [], "errors": [], "htmlHash": "f397053a", "data": {"in_app": false, "user": null, "domain": null, "domains": ["dujaw.com", "fgeta.com", "dennisgls26.com", "aipicz.com", "gamersparky26.com", "withsd.com", "zzetu.com", "dxgamers.com", "ulnik.com", "rdmail.info", "ziuwi.com", "tseru.com", "gohuki.com", "1em0nstore.win", "1em0nstore.trade", "1emonstore.trade"], "email": "<EMAIL>", "emails": ["<EMAIL>"], "captcha": null}, "dataMeta": [], "checksum": "e4fd09b9a8a51d7815c5f35c052c5683023634c03e473322b3e262912bb5df83"}}, "element_html": "<div wire:id=\"6XgJsXvp7aEW32c1i7Iv\" wire:initial-data='{\"fingerprint\":{\"id\":\"6XgJsXvp7aEW32c1i7Iv\",\"name\":\"frontend.actions\",\"locale\":\"en\",\"path\":\"mailbox\",\"method\":\"GET\",\"v\":\"acj\"},\"effects\":{\"listen"}, {"wire_id": "yqvx0O1MbmlxgJmVr1oE", "initial_data": {"fingerprint": {"id": "yqvx0O1MbmlxgJmVr1oE", "name": "frontend.nav", "locale": "en", "path": "mailbox", "method": "GET", "v": "acj"}, "effects": {"listeners": []}, "serverMemo": {"children": [], "errors": [], "htmlHash": "f3c6b6c8", "data": {"menus": [], "current_route": null}, "dataMeta": {"modelCollections": {"menus": {"class": null, "id": [], "relations": [], "connection": null}}}, "checksum": "4cee893e1627e84fe9fb31ab73bbeab75746881788f63e5b094c22aa32c8a239"}}, "element_html": "<nav wire:id=\"yqvx0O1MbmlxgJmVr1oE\" wire:initial-data='{\"fingerprint\":{\"id\":\"yqvx0O1MbmlxgJmVr1oE\",\"name\":\"frontend.nav\",\"locale\":\"en\",\"path\":\"mailbox\",\"method\":\"GET\",\"v\":\"acj\"},\"effects\":{\"listeners\""}, {"wire_id": "oDysGI3pZvUey4zz99w2", "initial_data": {"fingerprint": {"id": "oDysGI3pZvUey4zz99w2", "name": "frontend.app", "locale": "en", "path": "mailbox", "method": "GET", "v": "acj"}, "effects": {"listeners": ["fetchMessages", "syncEmail"]}, "serverMemo": {"children": [], "errors": [], "htmlHash": "36d69e5b", "data": {"messages": [], "deleted": [], "error": "", "email": "<EMAIL>", "initial": false, "overflow": false}, "dataMeta": [], "checksum": "14d8cd9906f7e3bc6f1d3176c7bb0911050949afd1394f6a6eb53d506561a5dc"}}, "element_html": "<main class=\"flex-1 lg:flex\" wire:id=\"oDysGI3pZvUey4zz99w2\" wire:initial-data='{\"fingerprint\":{\"id\":\"oDysGI3pZvUey4zz99w2\",\"name\":\"frontend.app\",\"locale\":\"en\",\"path\":\"mailbox\",\"method\":\"GET\",\"v\":\"acj\""}], "ajax_data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n        <title>Dujaw Store</title>\n        \n        <link rel=\"icon\" href=\"https://dujaw.com/storage/public/images/joystick.png\">\n        <link href=\"https://cdn.quilljs.com/1.3.6/quill.snow.css\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"preload\" as=\"style\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css\" integrity=\"sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==\" crossorigin=\"anonymous\" onload=\"this.onload=null;this.rel='stylesheet'\" />\n    <link rel=\"preload\" as=\"style\" href=\"https://dujaw.com/css/vendor.css\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"stylesheet\" href=\"https://dujaw.com/css/common.css\">\n    <script src=\"https://dujaw.com/vendor/Shortcode/Shortcode.js\"></script>\n    <script src=\"https://dujaw.com/js/app.js\" defer></script>\n    <style >[wire\\:loading], [wire\\:loading\\.delay], [wire\\:loading\\.inline-block], [wire\\:loading\\.inline], [wire\\:loading\\.block], [wire\\:loading\\.flex], [wire\\:loading\\.table], [wire\\:loading\\.grid], [wire\\:loading\\.inline-flex] {display: none;}[wire\\:loading\\.delay\\.shortest], [wire\\:loading\\.delay\\.shorter], [wire\\:loading\\.delay\\.short], [wire\\:loading\\.delay\\.long], [wire\\:loading\\.delay\\.longer], [wire\\:loading\\.delay\\.longest] {display:none;}[wire\\:offline] {display: none;}[wire\\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }</style>\n    \n        \n        <meta name=\"csrf-token\" content=\"kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n\">\n<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n<link href=\"https://fonts.googleapis.com/css2?family=Kadwa:wght@400;600;700&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<style>\n:root {\n  --head-font: \"Kadwa\";\n  --body-font: \"Poppins\";\n  --primary: #0155b5;\n  --secondary: #2fc10a;\n  --tertiary: #d2ab3e;\n}\n</style>\n<script>\n  let captcha_name = \"off\";\n  let site_key = \"\";\n  if(captcha_name && captcha_name !== \"off\") {\n    site_key = \"\";\n  }\n  let strings = {\"Get back to MailBox\":\"Get back to MailBox\",\"Enter Username\":\"Enter Username\",\"Select Domain\":\"Select Domain\",\"Create\":\"Create\",\"Random\":\"Random\",\"Custom\":\"Custom\",\"Menu\":\"Menu\",\"Cancel\":\"Cancel\",\"Copy\":\"Copy\",\"Refresh\":\"Refresh\",\"New\":\"New\",\"Delete\":\"Delete\",\"Download\":\"Download\",\"Fetching\":\"Fetching\",\"Empty Inbox\":\"Empty Inbox\",\"Error\":\"Error\",\"Success\":\"Success\",\"Close\":\"Close\",\"Email ID Copied to Clipboard\":\"Email ID Copied to Clipboard\",\"Please enter Username\":\"Please enter Username\",\"Please Select a Domain\":\"Please Select a Domain\",\"Username not allowed\":\"Username not allowed\",\"Your Temporary Email Address\":\"Your Temporary Email Address\",\"Attachments\":\"Attachments\",\"Blocked\":\"Blocked\",\"Emails from\":\"Emails from\",\"are blocked by Admin\":\"are blocked by Admin\",\"No Messages\":\"No Messages\",\"Waiting for Incoming Messages\":\"Waiting for Incoming Messages\",\"Scan QR Code to access\":\"Scan QR Code to access\",\"Create your own Temp Mail\":\"Create your own Temp Mail\",\"Your Temprorary Email\":\"Your Temprorary Email\",\"Enter a Username and Select the Domain\":\"Enter a Username and Select the Domain\",\"Username length cannot be less than\":\"Username length cannot be less than\",\"and greator than\":\"and greator than\",\"Create a Random Email\":\"Create a Random Email\",\"Sender\":\"Sender\",\"Subject\":\"Subject\",\"Time\":\"Time\",\"Open\":\"Open\",\"Go Back to Inbox\":\"Go Back to Inbox\",\"Date\":\"Date\",\"Copyright\":\"Copyright\",\"Ad Blocker Detected\":\"Ad Blocker Detected\",\"Disable the Ad Blocker to use \":\"Disable the Ad Blocker to use \",\"Your temporary email address is ready\":\"Your temporary email address is ready\",\"You have reached daily limit of MAX \":\"You have reached daily limit of MAX \",\" temp mail\":\" temp mail\",\"Sorry! That email is already been used by someone else. Please try a different email address.\":\"Sorry! That email is already been used by someone else. Please try a different email address.\",\"Invalid Captcha. Please try again\":\"Invalid Captcha. Please try again\",\"Invalid Password\":\"Invalid Password\",\"Password\":\"Password\",\"Unlock\":\"Unlock\",\"Your Name\":\"Your Name\",\"Enter your Name\":\"Enter your Name\",\"Your Email\":\"Your Email\",\"Enter your Email\":\"Enter your Email\",\"Message\":\"Message\",\"Enter your Message\":\"Enter your Message\",\"Send Message\":\"Send Message\"}\n  const __ = (string) => {\n    if(strings[string] !== undefined) {\n      return strings[string];\n    } else {\n      return string;\n    }\n  }\n</script>\n</head>\n<body>\n    <div class=\"default-theme\">\n        <div class=\"flex flex-wrap\">\n            <div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n                <div class=\"flex justify-center p-3 mb-10\">\n                    <a href=\"https://dujaw.com\">\n                                                <img class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\" alt=\"logo\">\n                                            </a>\n                </div>\n                                <div wire:id=\"lfdbEDYQfZo5UZ9M746X\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;lfdbEDYQfZo5UZ9M746X&quot;,&quot;name&quot;:&quot;frontend.actions&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;syncEmail&quot;,&quot;checkReCaptcha3&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;f397053a&quot;,&quot;data&quot;:{&quot;in_app&quot;:false,&quot;user&quot;:null,&quot;domain&quot;:null,&quot;domains&quot;:[&quot;dujaw.com&quot;,&quot;fgeta.com&quot;,&quot;dennisgls26.com&quot;,&quot;aipicz.com&quot;,&quot;gamersparky26.com&quot;,&quot;withsd.com&quot;,&quot;zzetu.com&quot;,&quot;dxgamers.com&quot;,&quot;ulnik.com&quot;,&quot;rdmail.info&quot;,&quot;ziuwi.com&quot;,&quot;tseru.com&quot;,&quot;gohuki.com&quot;,&quot;1em0nstore.win&quot;,&quot;1em0nstore.trade&quot;,&quot;1emonstore.trade&quot;],&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;emails&quot;:[&quot;<EMAIL>&quot;],&quot;captcha&quot;:null},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;e715f268c117cd86cc2fc6ffbd445c6606f3f043187b24d9015951b530dc63d1&quot;}}\" x-data=\"{ in_app: false }\">\n    <div x-show.transition.in=\"in_app\" class=\"app-action mt-4 px-8\" style=\"display: none;\">\n                <form wire:submit.prevent=\"create\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n                        <input class=\"block appearance-none w-full border-0 rounded-md py-4 px-5 bg-white text-white bg-opacity-10 focus:outline-none placeholder-white placeholder-opacity-50\" type=\"text\" name=\"user\" id=\"user\" wire:model.defer=\"user\" placeholder=\"Enter Username\">\n            <div class=\"divider mt-5\"></div>\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <input x-ref=\"domain\" type=\"text\" class=\"block appearance-none w-full border-0 bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none placeholder-white placeholder-opacity-50\" placeholder=\"Select Domain\" name=\"domain\" id=\"domain\" wire:model=\"domain\" readonly>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top-right right-0\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a x-on:click=\"$refs.domain.value = 'dujaw.com'; $wire.setDomain('dujaw.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dujaw.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'fgeta.com'; $wire.setDomain('fgeta.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>fgeta.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dennisgls26.com'; $wire.setDomain('dennisgls26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dennisgls26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'aipicz.com'; $wire.setDomain('aipicz.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>aipicz.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gamersparky26.com'; $wire.setDomain('gamersparky26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gamersparky26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'withsd.com'; $wire.setDomain('withsd.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>withsd.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'zzetu.com'; $wire.setDomain('zzetu.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>zzetu.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dxgamers.com'; $wire.setDomain('dxgamers.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dxgamers.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ulnik.com'; $wire.setDomain('ulnik.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ulnik.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'rdmail.info'; $wire.setDomain('rdmail.info')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>rdmail.info</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ziuwi.com'; $wire.setDomain('ziuwi.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ziuwi.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'tseru.com'; $wire.setDomain('tseru.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>tseru.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gohuki.com'; $wire.setDomain('gohuki.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gohuki.com</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.win'; $wire.setDomain('1em0nstore.win')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.win</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.trade'; $wire.setDomain('1em0nstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.trade</a>\n                                                <a x-on:click=\"$refs.domain.value = '1emonstore.trade'; $wire.setDomain('1emonstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1emonstore.trade</a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-5 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n            <div class=\"divider mt-5\"></div>\n            <input id=\"create\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-teal-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #2fc10a\" type=\"submit\" value=\"Create\">\n            <div class=\"divider my-8 flex justify-center\">\n                <div class=\"border-t-2 w-2/3 border-white border-opacity-25\"></div>\n            </div>\n        </form>\n        <form wire:submit.prevent=\"random\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n            <input id=\"random\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-yellow-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #d2ab3e\" type=\"submit\" value=\"Random\">\n        </form>\n                <div class=\"lg:max-w-72 lg:mx-auto\">\n            <button x-on:click=\"in_app = false\" class=\"block appearance-none w-full rounded-md my-5 py-2 px-5 bg-white bg-opacity-10 text-white text-sm cursor-pointer focus:outline-none\">Cancel</button>\n        </div>\n            </div>\n    <div x-show.transition.in=\"!in_app\" class=\"in-app-actions mt-4 px-8\" style=\"display: none;\">\n        <form class=\"lg:max-w-72 lg:mx-auto\" action=\"#\" method=\"post\">\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\"><EMAIL></a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n        </form>\n        <div class=\"divider mt-5\"></div>\n        <div class=\"grid grid-cols-4 lg:grid-cols-2 gap-2 lg:gap-6 lg:max-w-72 lg:mx-auto\">\n            <div class=\"btn_copy bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl mx-auto\">\n                    <i class=\"far fa-copy\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Copy</div>\n            </div>\n            <div onclick=\"document.getElementById('refresh').classList.remove('pause-spinner')\" wire:click=\"$emit('fetchMessages')\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i id=\"refresh\" class=\"fas fa-sync-alt fa-spin\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Refresh</div>\n            </div>\n            <div x-on:click=\"in_app = true\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-plus-square\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">New</div>\n            </div>\n            <div wire:click=\"deleteEmail\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-trash-alt\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Delete</div>\n            </div>\n        </div>\n    </div>\n    </div>\n<!-- Livewire Component wire-end:lfdbEDYQfZo5UZ9M746X -->                            </div>\n            <div class=\"w-full lg:w-3/4\">\n                <nav wire:id=\"VLa35w6kCZKINACExIwI\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;VLa35w6kCZKINACExIwI&quot;,&quot;name&quot;:&quot;frontend.nav&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;f3c6b6c8&quot;,&quot;data&quot;:{&quot;menus&quot;:[],&quot;current_route&quot;:null},&quot;dataMeta&quot;:{&quot;modelCollections&quot;:{&quot;menus&quot;:{&quot;class&quot;:null,&quot;id&quot;:[],&quot;relations&quot;:[],&quot;connection&quot;:null}}},&quot;checksum&quot;:&quot;ff6235aa7c0e8c0d793af2e389e3717a804e0d10169c8c310570e38c8b71cde7&quot;}}\">\n    <div class=\"bg-gray-100 px-5 hidden lg:flex sticky top-0 z-40 h-24\">\n        <div class=\"w-full my-auto\">\n            <div class=\"flex items-center justify-between h-16\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex items-baseline space-x-4\">\n                                                                    </div>\n                </div>\n                <div class=\"flex items-center\">\n                    <div>\n                                            </div>\n                    <div class=\"ml-4 flex items-center md:ml-6\">\n                        <div class=\"relative\">\n                            <form action=\"https://dujaw.com/locale\" id=\"locale-form\" method=\"post\">\n                                <input type=\"hidden\" name=\"_token\" value=\"kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n\">                                <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale\">\n                                                                        <option >ar</option>\n                                                                        <option >de</option>\n                                                                        <option selected>en</option>\n                                                                        <option >fr</option>\n                                                                        <option >hi</option>\n                                                                        <option >pl</option>\n                                                                        <option >ru</option>\n                                                                        <option >es</option>\n                                                                        <option >vi</option>\n                                                                        <option >tr</option>\n                                                                        <option >no</option>\n                                                                        <option >id</option>\n                                                                        <option >it</option>\n                                                                    </select>\n                            </form>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <div x-data=\"{ open: false }\">\n        <div @click=\"open = true\" class=\"absolute top-12 right-6 w-8 text-white\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16m-7 6h7\" />\n            </svg>\n        </div>\n        <div x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:leave=\"transition ease-in duration-75\" x-transition:leave-start=\"transform opacity-100 scale-100\" x-transition:leave-end=\"transform opacity-0 scale-95\" @click.away=\"open = false\" class=\"flex-col lg:hidden fixed top-0 left-0 min-h-screen w-full bg-black bg-opacity-75\">\n            <div @click=\"open = false\" class=\"absolute top-6 right-6 w-8 text-white\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n            </div>\n            <div class=\"w-full mx-auto mt-20\">\n                <div class=\"flex flex-col items-center justify-between\">\n                    <div class=\"flex flex-col items-center space-y-2\">\n                                                                    </div>\n                    <div class=\"flex flex-col items-center space-y-2 mt-10\">\n                        <div class=\"text-white space-x-2\">\n                                                    </div>\n                        <div class=\"flex items-center mt-4\">\n                            <div class=\"relative\">\n                                <form action=\"https://dujaw.com/locale\" id=\"locale-form-mobile\" method=\"post\">\n                                    <input type=\"hidden\" name=\"_token\" value=\"kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n\">                                    <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale-mobile\">\n                                                                                <option >ar</option>\n                                                                                <option >de</option>\n                                                                                <option selected>en</option>\n                                                                                <option >fr</option>\n                                                                                <option >hi</option>\n                                                                                <option >pl</option>\n                                                                                <option >ru</option>\n                                                                                <option >es</option>\n                                                                                <option >vi</option>\n                                                                                <option >tr</option>\n                                                                                <option >no</option>\n                                                                                <option >id</option>\n                                                                                <option >it</option>\n                                                                            </select>\n                                </form>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</nav>\n<!-- Livewire Component wire-end:VLa35w6kCZKINACExIwI -->                <div class=\"flex flex-col lg:min-h-tm-default\">\n                                         \n                        <main wire:id=\"iaEdcmuH6Zt8yYlPojWs\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;iaEdcmuH6Zt8yYlPojWs&quot;,&quot;name&quot;:&quot;frontend.app&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;fetchMessages&quot;,&quot;syncEmail&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;36d69e5b&quot;,&quot;data&quot;:{&quot;messages&quot;:[],&quot;deleted&quot;:[],&quot;error&quot;:&quot;&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;initial&quot;:false,&quot;overflow&quot;:false},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;fbf8489e385c7a94c6ad2764933de9c5c97efc326c2ee85f6899ff1d8bd139ae&quot;}}\" x-data=\"{ id: 0 }\" class=\"flex-1 lg:flex\">\n            <div class=\"w-full lg:w-1/3 bg-white flex flex-col min-h-tm-mobile\">\n                <div class=\"flex-1 flex justify-center items-center h-40 text-gray-400 text-2xl\">\n            Fetching...\n        </div>\n            </div>\n    <div class=\"message-content w-full lg:w-2/3 bg-white border-1 border-l border-gray-200 flex flex-col\">\n        <div x-show=\"id === 0\" class=\"flex-1 hidden lg:flex\">\n            <div class=\"w-2/3 m-auto\">\n                <img class=\"m-auto max-w-full\" src=\"https://dujaw.com/images/sample.jpg\" alt=\"mails\">\n            </div>\n        </div>\n            </div>\n</main>\n<!-- Livewire Component wire-end:iaEdcmuH6Zt8yYlPojWs -->                                                        </div>\n            </div>\n        </div>\n    </div>\n    \n    <!--- Helper Text for Language Translation -->\n    <div class=\"hidden language-helper\">\n        <div class=\"error\">Error</div>\n        <div class=\"success\">Success</div>\n        <div class=\"copy_text\">Email ID Copied to Clipboard</div>\n    </div>\n\n    <script src=\"/livewire/livewire.js?id=90730a3b0e7144480175\" data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" ></script><script data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" >window.livewire = new Livewire();window.Livewire = window.livewire;window.livewire_app_url = '';window.livewire_token = 'kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n';window.deferLoadingAlpine = function (callback) {window.addEventListener('livewire:load', function () {callback();});};let started = false;window.addEventListener('alpine:initializing', function () {if (! started) {window.livewire.start();started = true;}});document.addEventListener(\"DOMContentLoaded\", function () {if (! started) {window.livewire.start();started = true;}});</script>\n        <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const email = '<EMAIL>';\n            const add_mail_in_title = \"yes\"\n            if(add_mail_in_title === 'yes') {\n                document.title += ` - ${email}`;\n            }\n            Livewire.emit('syncEmail', email);\n            Livewire.emit('fetchMessages');\n        });\n    </script>\n        <script>\n        document.addEventListener('stopLoader', () => {\n            document.getElementById('refresh').classList.add('pause-spinner');\n        });\n        let counter = parseInt(20);\n        setInterval(() => {\n            if (counter === 0 && document.getElementById('imap-error') === null && !document.hidden) {\n                document.getElementById('refresh').classList.remove('pause-spinner');\n                Livewire.emit('fetchMessages');\n                counter = parseInt(20);\n            }\n            counter--;\n            if(document.hidden) {\n                counter = 1;\n            }\n        }, 1000);\n    </script>\n    \n    \n        <script src=\"https://dujaw.com/storage/js/mnpw3.js\" defer></script>\n    <script defer>\n    setTimeout(() => {\n        const enable_ad_block_detector = \"0\"\n        if(!document.getElementById('Q8CvrZzY9fphm6gG') && enable_ad_block_detector == \"1\") {\n            document.querySelector('.default-theme').remove()\n            document.querySelector('body > div').insertAdjacentHTML('beforebegin', `\n                <div class=\"fixed w-screen h-screen bg-red-800 flex flex-col justify-center items-center gap-5 z-50 text-white\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-40 w-40\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fill-rule=\"evenodd\" d=\"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z\" clip-rule=\"evenodd\" />\n                    </svg>\n                    <h1 class=\"text-4xl font-bold\">Ad Blocker Detected</h1>\n                    <h2>Disable the Ad Blocker to use Dujaw Store</h2>\n                </div>\n            `)\n        }\n    }, 500);\n    </script>\n    </body>\n</html>", "correos_encontrados": [{"source": "ajax", "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n        <title>Dujaw Store</title>\n        \n        <link rel=\"icon\" href=\"https://dujaw.com/storage/public/images/joystick.png\">\n        <link href=\"https://cdn.quilljs.com/1.3.6/quill.snow.css\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"preload\" as=\"style\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css\" integrity=\"sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==\" crossorigin=\"anonymous\" onload=\"this.onload=null;this.rel='stylesheet'\" />\n    <link rel=\"preload\" as=\"style\" href=\"https://dujaw.com/css/vendor.css\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"stylesheet\" href=\"https://dujaw.com/css/common.css\">\n    <script src=\"https://dujaw.com/vendor/Shortcode/Shortcode.js\"></script>\n    <script src=\"https://dujaw.com/js/app.js\" defer></script>\n    <style >[wire\\:loading], [wire\\:loading\\.delay], [wire\\:loading\\.inline-block], [wire\\:loading\\.inline], [wire\\:loading\\.block], [wire\\:loading\\.flex], [wire\\:loading\\.table], [wire\\:loading\\.grid], [wire\\:loading\\.inline-flex] {display: none;}[wire\\:loading\\.delay\\.shortest], [wire\\:loading\\.delay\\.shorter], [wire\\:loading\\.delay\\.short], [wire\\:loading\\.delay\\.long], [wire\\:loading\\.delay\\.longer], [wire\\:loading\\.delay\\.longest] {display:none;}[wire\\:offline] {display: none;}[wire\\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }</style>\n    \n        \n        <meta name=\"csrf-token\" content=\"kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n\">\n<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n<link href=\"https://fonts.googleapis.com/css2?family=Kadwa:wght@400;600;700&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<style>\n:root {\n  --head-font: \"Kadwa\";\n  --body-font: \"Poppins\";\n  --primary: #0155b5;\n  --secondary: #2fc10a;\n  --tertiary: #d2ab3e;\n}\n</style>\n<script>\n  let captcha_name = \"off\";\n  let site_key = \"\";\n  if(captcha_name && captcha_name !== \"off\") {\n    site_key = \"\";\n  }\n  let strings = {\"Get back to MailBox\":\"Get back to MailBox\",\"Enter Username\":\"Enter Username\",\"Select Domain\":\"Select Domain\",\"Create\":\"Create\",\"Random\":\"Random\",\"Custom\":\"Custom\",\"Menu\":\"Menu\",\"Cancel\":\"Cancel\",\"Copy\":\"Copy\",\"Refresh\":\"Refresh\",\"New\":\"New\",\"Delete\":\"Delete\",\"Download\":\"Download\",\"Fetching\":\"Fetching\",\"Empty Inbox\":\"Empty Inbox\",\"Error\":\"Error\",\"Success\":\"Success\",\"Close\":\"Close\",\"Email ID Copied to Clipboard\":\"Email ID Copied to Clipboard\",\"Please enter Username\":\"Please enter Username\",\"Please Select a Domain\":\"Please Select a Domain\",\"Username not allowed\":\"Username not allowed\",\"Your Temporary Email Address\":\"Your Temporary Email Address\",\"Attachments\":\"Attachments\",\"Blocked\":\"Blocked\",\"Emails from\":\"Emails from\",\"are blocked by Admin\":\"are blocked by Admin\",\"No Messages\":\"No Messages\",\"Waiting for Incoming Messages\":\"Waiting for Incoming Messages\",\"Scan QR Code to access\":\"Scan QR Code to access\",\"Create your own Temp Mail\":\"Create your own Temp Mail\",\"Your Temprorary Email\":\"Your Temprorary Email\",\"Enter a Username and Select the Domain\":\"Enter a Username and Select the Domain\",\"Username length cannot be less than\":\"Username length cannot be less than\",\"and greator than\":\"and greator than\",\"Create a Random Email\":\"Create a Random Email\",\"Sender\":\"Sender\",\"Subject\":\"Subject\",\"Time\":\"Time\",\"Open\":\"Open\",\"Go Back to Inbox\":\"Go Back to Inbox\",\"Date\":\"Date\",\"Copyright\":\"Copyright\",\"Ad Blocker Detected\":\"Ad Blocker Detected\",\"Disable the Ad Blocker to use \":\"Disable the Ad Blocker to use \",\"Your temporary email address is ready\":\"Your temporary email address is ready\",\"You have reached daily limit of MAX \":\"You have reached daily limit of MAX \",\" temp mail\":\" temp mail\",\"Sorry! That email is already been used by someone else. Please try a different email address.\":\"Sorry! That email is already been used by someone else. Please try a different email address.\",\"Invalid Captcha. Please try again\":\"Invalid Captcha. Please try again\",\"Invalid Password\":\"Invalid Password\",\"Password\":\"Password\",\"Unlock\":\"Unlock\",\"Your Name\":\"Your Name\",\"Enter your Name\":\"Enter your Name\",\"Your Email\":\"Your Email\",\"Enter your Email\":\"Enter your Email\",\"Message\":\"Message\",\"Enter your Message\":\"Enter your Message\",\"Send Message\":\"Send Message\"}\n  const __ = (string) => {\n    if(strings[string] !== undefined) {\n      return strings[string];\n    } else {\n      return string;\n    }\n  }\n</script>\n</head>\n<body>\n    <div class=\"default-theme\">\n        <div class=\"flex flex-wrap\">\n            <div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n                <div class=\"flex justify-center p-3 mb-10\">\n                    <a href=\"https://dujaw.com\">\n                                                <img class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\" alt=\"logo\">\n                                            </a>\n                </div>\n                                <div wire:id=\"lfdbEDYQfZo5UZ9M746X\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;lfdbEDYQfZo5UZ9M746X&quot;,&quot;name&quot;:&quot;frontend.actions&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;syncEmail&quot;,&quot;checkReCaptcha3&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;f397053a&quot;,&quot;data&quot;:{&quot;in_app&quot;:false,&quot;user&quot;:null,&quot;domain&quot;:null,&quot;domains&quot;:[&quot;dujaw.com&quot;,&quot;fgeta.com&quot;,&quot;dennisgls26.com&quot;,&quot;aipicz.com&quot;,&quot;gamersparky26.com&quot;,&quot;withsd.com&quot;,&quot;zzetu.com&quot;,&quot;dxgamers.com&quot;,&quot;ulnik.com&quot;,&quot;rdmail.info&quot;,&quot;ziuwi.com&quot;,&quot;tseru.com&quot;,&quot;gohuki.com&quot;,&quot;1em0nstore.win&quot;,&quot;1em0nstore.trade&quot;,&quot;1emonstore.trade&quot;],&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;emails&quot;:[&quot;<EMAIL>&quot;],&quot;captcha&quot;:null},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;e715f268c117cd86cc2fc6ffbd445c6606f3f043187b24d9015951b530dc63d1&quot;}}\" x-data=\"{ in_app: false }\">\n    <div x-show.transition.in=\"in_app\" class=\"app-action mt-4 px-8\" style=\"display: none;\">\n                <form wire:submit.prevent=\"create\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n                        <input class=\"block appearance-none w-full border-0 rounded-md py-4 px-5 bg-white text-white bg-opacity-10 focus:outline-none placeholder-white placeholder-opacity-50\" type=\"text\" name=\"user\" id=\"user\" wire:model.defer=\"user\" placeholder=\"Enter Username\">\n            <div class=\"divider mt-5\"></div>\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <input x-ref=\"domain\" type=\"text\" class=\"block appearance-none w-full border-0 bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none placeholder-white placeholder-opacity-50\" placeholder=\"Select Domain\" name=\"domain\" id=\"domain\" wire:model=\"domain\" readonly>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top-right right-0\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a x-on:click=\"$refs.domain.value = 'dujaw.com'; $wire.setDomain('dujaw.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dujaw.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'fgeta.com'; $wire.setDomain('fgeta.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>fgeta.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dennisgls26.com'; $wire.setDomain('dennisgls26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dennisgls26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'aipicz.com'; $wire.setDomain('aipicz.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>aipicz.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gamersparky26.com'; $wire.setDomain('gamersparky26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gamersparky26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'withsd.com'; $wire.setDomain('withsd.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>withsd.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'zzetu.com'; $wire.setDomain('zzetu.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>zzetu.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dxgamers.com'; $wire.setDomain('dxgamers.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dxgamers.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ulnik.com'; $wire.setDomain('ulnik.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ulnik.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'rdmail.info'; $wire.setDomain('rdmail.info')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>rdmail.info</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ziuwi.com'; $wire.setDomain('ziuwi.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ziuwi.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'tseru.com'; $wire.setDomain('tseru.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>tseru.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gohuki.com'; $wire.setDomain('gohuki.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gohuki.com</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.win'; $wire.setDomain('1em0nstore.win')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.win</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.trade'; $wire.setDomain('1em0nstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.trade</a>\n                                                <a x-on:click=\"$refs.domain.value = '1emonstore.trade'; $wire.setDomain('1emonstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1emonstore.trade</a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-5 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n            <div class=\"divider mt-5\"></div>\n            <input id=\"create\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-teal-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #2fc10a\" type=\"submit\" value=\"Create\">\n            <div class=\"divider my-8 flex justify-center\">\n                <div class=\"border-t-2 w-2/3 border-white border-opacity-25\"></div>\n            </div>\n        </form>\n        <form wire:submit.prevent=\"random\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n            <input id=\"random\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-yellow-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #d2ab3e\" type=\"submit\" value=\"Random\">\n        </form>\n                <div class=\"lg:max-w-72 lg:mx-auto\">\n            <button x-on:click=\"in_app = false\" class=\"block appearance-none w-full rounded-md my-5 py-2 px-5 bg-white bg-opacity-10 text-white text-sm cursor-pointer focus:outline-none\">Cancel</button>\n        </div>\n            </div>\n    <div x-show.transition.in=\"!in_app\" class=\"in-app-actions mt-4 px-8\" style=\"display: none;\">\n        <form class=\"lg:max-w-72 lg:mx-auto\" action=\"#\" method=\"post\">\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\"><EMAIL></a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n        </form>\n        <div class=\"divider mt-5\"></div>\n        <div class=\"grid grid-cols-4 lg:grid-cols-2 gap-2 lg:gap-6 lg:max-w-72 lg:mx-auto\">\n            <div class=\"btn_copy bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl mx-auto\">\n                    <i class=\"far fa-copy\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Copy</div>\n            </div>\n            <div onclick=\"document.getElementById('refresh').classList.remove('pause-spinner')\" wire:click=\"$emit('fetchMessages')\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i id=\"refresh\" class=\"fas fa-sync-alt fa-spin\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Refresh</div>\n            </div>\n            <div x-on:click=\"in_app = true\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-plus-square\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">New</div>\n            </div>\n            <div wire:click=\"deleteEmail\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-trash-alt\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Delete</div>\n            </div>\n        </div>\n    </div>\n    </div>\n<!-- Livewire Component wire-end:lfdbEDYQfZo5UZ9M746X -->                            </div>\n            <div class=\"w-full lg:w-3/4\">\n                <nav wire:id=\"VLa35w6kCZKINACExIwI\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;VLa35w6kCZKINACExIwI&quot;,&quot;name&quot;:&quot;frontend.nav&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;f3c6b6c8&quot;,&quot;data&quot;:{&quot;menus&quot;:[],&quot;current_route&quot;:null},&quot;dataMeta&quot;:{&quot;modelCollections&quot;:{&quot;menus&quot;:{&quot;class&quot;:null,&quot;id&quot;:[],&quot;relations&quot;:[],&quot;connection&quot;:null}}},&quot;checksum&quot;:&quot;ff6235aa7c0e8c0d793af2e389e3717a804e0d10169c8c310570e38c8b71cde7&quot;}}\">\n    <div class=\"bg-gray-100 px-5 hidden lg:flex sticky top-0 z-40 h-24\">\n        <div class=\"w-full my-auto\">\n            <div class=\"flex items-center justify-between h-16\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex items-baseline space-x-4\">\n                                                                    </div>\n                </div>\n                <div class=\"flex items-center\">\n                    <div>\n                                            </div>\n                    <div class=\"ml-4 flex items-center md:ml-6\">\n                        <div class=\"relative\">\n                            <form action=\"https://dujaw.com/locale\" id=\"locale-form\" method=\"post\">\n                                <input type=\"hidden\" name=\"_token\" value=\"kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n\">                                <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale\">\n                                                                        <option >ar</option>\n                                                                        <option >de</option>\n                                                                        <option selected>en</option>\n                                                                        <option >fr</option>\n                                                                        <option >hi</option>\n                                                                        <option >pl</option>\n                                                                        <option >ru</option>\n                                                                        <option >es</option>\n                                                                        <option >vi</option>\n                                                                        <option >tr</option>\n                                                                        <option >no</option>\n                                                                        <option >id</option>\n                                                                        <option >it</option>\n                                                                    </select>\n                            </form>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <div x-data=\"{ open: false }\">\n        <div @click=\"open = true\" class=\"absolute top-12 right-6 w-8 text-white\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16m-7 6h7\" />\n            </svg>\n        </div>\n        <div x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:leave=\"transition ease-in duration-75\" x-transition:leave-start=\"transform opacity-100 scale-100\" x-transition:leave-end=\"transform opacity-0 scale-95\" @click.away=\"open = false\" class=\"flex-col lg:hidden fixed top-0 left-0 min-h-screen w-full bg-black bg-opacity-75\">\n            <div @click=\"open = false\" class=\"absolute top-6 right-6 w-8 text-white\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n            </div>\n            <div class=\"w-full mx-auto mt-20\">\n                <div class=\"flex flex-col items-center justify-between\">\n                    <div class=\"flex flex-col items-center space-y-2\">\n                                                                    </div>\n                    <div class=\"flex flex-col items-center space-y-2 mt-10\">\n                        <div class=\"text-white space-x-2\">\n                                                    </div>\n                        <div class=\"flex items-center mt-4\">\n                            <div class=\"relative\">\n                                <form action=\"https://dujaw.com/locale\" id=\"locale-form-mobile\" method=\"post\">\n                                    <input type=\"hidden\" name=\"_token\" value=\"kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n\">                                    <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale-mobile\">\n                                                                                <option >ar</option>\n                                                                                <option >de</option>\n                                                                                <option selected>en</option>\n                                                                                <option >fr</option>\n                                                                                <option >hi</option>\n                                                                                <option >pl</option>\n                                                                                <option >ru</option>\n                                                                                <option >es</option>\n                                                                                <option >vi</option>\n                                                                                <option >tr</option>\n                                                                                <option >no</option>\n                                                                                <option >id</option>\n                                                                                <option >it</option>\n                                                                            </select>\n                                </form>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</nav>\n<!-- Livewire Component wire-end:VLa35w6kCZKINACExIwI -->                <div class=\"flex flex-col lg:min-h-tm-default\">\n                                         \n                        <main wire:id=\"iaEdcmuH6Zt8yYlPojWs\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;iaEdcmuH6Zt8yYlPojWs&quot;,&quot;name&quot;:&quot;frontend.app&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;fetchMessages&quot;,&quot;syncEmail&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;36d69e5b&quot;,&quot;data&quot;:{&quot;messages&quot;:[],&quot;deleted&quot;:[],&quot;error&quot;:&quot;&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;initial&quot;:false,&quot;overflow&quot;:false},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;fbf8489e385c7a94c6ad2764933de9c5c97efc326c2ee85f6899ff1d8bd139ae&quot;}}\" x-data=\"{ id: 0 }\" class=\"flex-1 lg:flex\">\n            <div class=\"w-full lg:w-1/3 bg-white flex flex-col min-h-tm-mobile\">\n                <div class=\"flex-1 flex justify-center items-center h-40 text-gray-400 text-2xl\">\n            Fetching...\n        </div>\n            </div>\n    <div class=\"message-content w-full lg:w-2/3 bg-white border-1 border-l border-gray-200 flex flex-col\">\n        <div x-show=\"id === 0\" class=\"flex-1 hidden lg:flex\">\n            <div class=\"w-2/3 m-auto\">\n                <img class=\"m-auto max-w-full\" src=\"https://dujaw.com/images/sample.jpg\" alt=\"mails\">\n            </div>\n        </div>\n            </div>\n</main>\n<!-- Livewire Component wire-end:iaEdcmuH6Zt8yYlPojWs -->                                                        </div>\n            </div>\n        </div>\n    </div>\n    \n    <!--- Helper Text for Language Translation -->\n    <div class=\"hidden language-helper\">\n        <div class=\"error\">Error</div>\n        <div class=\"success\">Success</div>\n        <div class=\"copy_text\">Email ID Copied to Clipboard</div>\n    </div>\n\n    <script src=\"/livewire/livewire.js?id=90730a3b0e7144480175\" data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" ></script><script data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" >window.livewire = new Livewire();window.Livewire = window.livewire;window.livewire_app_url = '';window.livewire_token = 'kNWyOL3Xusyj5T8Mf6OkehFzUFPM7Xw1wo6SL17n';window.deferLoadingAlpine = function (callback) {window.addEventListener('livewire:load', function () {callback();});};let started = false;window.addEventListener('alpine:initializing', function () {if (! started) {window.livewire.start();started = true;}});document.addEventListener(\"DOMContentLoaded\", function () {if (! started) {window.livewire.start();started = true;}});</script>\n        <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const email = '<EMAIL>';\n            const add_mail_in_title = \"yes\"\n            if(add_mail_in_title === 'yes') {\n                document.title += ` - ${email}`;\n            }\n            Livewire.emit('syncEmail', email);\n            Livewire.emit('fetchMessages');\n        });\n    </script>\n        <script>\n        document.addEventListener('stopLoader', () => {\n            document.getElementById('refresh').classList.add('pause-spinner');\n        });\n        let counter = parseInt(20);\n        setInterval(() => {\n            if (counter === 0 && document.getElementById('imap-error') === null && !document.hidden) {\n                document.getElementById('refresh').classList.remove('pause-spinner');\n                Livewire.emit('fetchMessages');\n                counter = parseInt(20);\n            }\n            counter--;\n            if(document.hidden) {\n                counter = 1;\n            }\n        }, 1000);\n    </script>\n    \n    \n        <script src=\"https://dujaw.com/storage/js/mnpw3.js\" defer></script>\n    <script defer>\n    setTimeout(() => {\n        const enable_ad_block_detector = \"0\"\n        if(!document.getElementById('Q8CvrZzY9fphm6gG') && enable_ad_block_detector == \"1\") {\n            document.querySelector('.default-theme').remove()\n            document.querySelector('body > div').insertAdjacentHTML('beforebegin', `\n                <div class=\"fixed w-screen h-screen bg-red-800 flex flex-col justify-center items-center gap-5 z-50 text-white\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-40 w-40\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fill-rule=\"evenodd\" d=\"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z\" clip-rule=\"evenodd\" />\n                    </svg>\n                    <h1 class=\"text-4xl font-bold\">Ad Blocker Detected</h1>\n                    <h2>Disable the Ad Blocker to use Dujaw Store</h2>\n                </div>\n            `)\n        }\n    }, 500);\n    </script>\n    </body>\n</html>"}]}