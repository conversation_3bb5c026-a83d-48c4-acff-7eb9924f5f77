#!/usr/bin/env python3
"""
Script para leer correos usando Playwright
Ejecuta JavaScript para cargar los correos dinámicamente
"""

import asyncio
import json
import time
from datetime import datetime


async def leer_correos_con_playwright():
    """Lee los correos usando Playwright para ejecutar JavaScript"""
    
    try:
        # Importar Playwright
        from playwright.async_api import async_playwright
        
        print("🚀 LEYENDO CORREOS CON PLAYWRIGHT")
        print("=" * 50)
        
        async with async_playwright() as p:
            # Lanzar navegador
            browser = await p.chromium.launch(headless=False)  # Modo visual para debug
            context = await browser.new_context()
            page = await context.new_page()
            
            # Variables para capturar requests
            captured_requests = []
            captured_responses = []
            
            # Interceptar requests
            async def handle_request(request):
                captured_requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(await request.all_headers()),
                    'post_data': request.post_data
                })
                print(f"📤 REQUEST: {request.method} {request.url}")
                
            async def handle_response(response):
                if 'json' in response.headers.get('content-type', '').lower() or '/api/' in response.url:
                    try:
                        content = await response.text()
                        captured_responses.append({
                            'url': response.url,
                            'status': response.status,
                            'headers': dict(await response.all_headers()),
                            'content': content
                        })
                        print(f"📥 RESPONSE: {response.status} {response.url}")
                        if content and len(content) < 1000:
                            print(f"   Content: {content[:200]}...")
                    except:
                        pass
                        
            page.on('request', handle_request)
            page.on('response', handle_response)
            
            # Navegar al mailbox
            url = "https://dujaw.com/mailbox/<EMAIL>"
            print(f"🌐 Navegando a: {url}")
            
            await page.goto(url, wait_until='networkidle')
            await page.screenshot(path='step1_initial.png')
            
            # Buscar y llenar el campo de contraseña
            print("🔐 Realizando unlock...")
            
            password_field = await page.wait_for_selector('input[type="password"]', timeout=10000)
            await password_field.fill('unlockgs2024')
            await page.screenshot(path='step2_password_filled.png')
            
            # Hacer click en unlock
            unlock_button = await page.wait_for_selector('button[type="submit"]', timeout=5000)
            await unlock_button.click()
            
            # Esperar a que se cargue el mailbox
            print("⏳ Esperando que se cargue el mailbox...")
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(5)  # Esperar a que se ejecute el JavaScript
            await page.screenshot(path='step3_mailbox_loaded.png')
            
            # Buscar elementos de correos en la página
            print("🔍 Buscando correos en la página...")
            
            # Esperar a que aparezcan los correos (pueden tardar en cargar)
            await asyncio.sleep(3)
            
            # Buscar diferentes selectores que podrían contener correos
            selectors_to_try = [
                '[wire\\:key]',  # Elementos de Livewire
                '.message',
                '.email',
                '.mail-item',
                '.inbox-item',
                'tr',  # Filas de tabla
                'li',  # Elementos de lista
                '[class*="message"]',
                '[class*="email"]',
                '[class*="mail"]',
                'div[id*="message"]',
                'div[id*="email"]'
            ]
            
            found_emails = []
            
            for selector in selectors_to_try:
                try:
                    elements = await page.query_selector_all(selector)
                    print(f"🔍 Selector '{selector}': {len(elements)} elementos")
                    
                    for i, element in enumerate(elements):
                        try:
                            text = await element.inner_text()
                            html = await element.inner_html()
                            
                            # Filtrar elementos que parezcan correos
                            if (text and len(text) > 30 and 
                                ('@' in text or 'subject' in text.lower() or 
                                 'from' in text.lower() or 'to' in text.lower())):
                                
                                found_emails.append({
                                    'selector': selector,
                                    'index': i,
                                    'text': text,
                                    'html': html[:500],  # Limitar HTML
                                    'length': len(text)
                                })
                                print(f"  📧 Posible correo encontrado: {text[:100]}...")
                                
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    print(f"❌ Error con selector {selector}: {e}")
                    
            # Intentar ejecutar JavaScript para obtener datos
            print("🔄 Ejecutando JavaScript para obtener datos...")
            
            try:
                # Intentar obtener datos de Livewire
                livewire_data = await page.evaluate("""
                    () => {
                        // Buscar datos de Livewire
                        if (window.livewire && window.livewire.components) {
                            return Object.values(window.livewire.components).map(comp => comp.data);
                        }
                        return null;
                    }
                """)
                
                if livewire_data:
                    print(f"✅ Datos de Livewire obtenidos: {len(livewire_data)} componentes")
                    found_emails.append({
                        'source': 'livewire_js',
                        'data': livewire_data
                    })
                    
            except Exception as e:
                print(f"❌ Error ejecutando JavaScript: {e}")
                
            # Buscar en el DOM elementos que contengan texto de email
            print("🔍 Buscando texto de email en el DOM...")
            
            try:
                email_texts = await page.evaluate("""
                    () => {
                        const walker = document.createTreeWalker(
                            document.body,
                            NodeFilter.SHOW_TEXT,
                            null,
                            false
                        );
                        
                        const emailTexts = [];
                        let node;
                        
                        while (node = walker.nextNode()) {
                            const text = node.textContent.trim();
                            if (text.length > 20 && (
                                text.includes('@') || 
                                text.toLowerCase().includes('subject') ||
                                text.toLowerCase().includes('from') ||
                                text.toLowerCase().includes('message')
                            )) {
                                emailTexts.push({
                                    text: text,
                                    parent: node.parentElement ? node.parentElement.tagName : 'unknown'
                                });
                            }
                        }
                        
                        return emailTexts;
                    }
                """)
                
                if email_texts:
                    print(f"✅ Textos de email encontrados: {len(email_texts)}")
                    for i, email_text in enumerate(email_texts[:5], 1):  # Mostrar solo los primeros 5
                        print(f"  {i}. {email_text['text'][:100]}...")
                        
                    found_emails.append({
                        'source': 'dom_text',
                        'data': email_texts
                    })
                    
            except Exception as e:
                print(f"❌ Error buscando texto en DOM: {e}")
                
            # Tomar screenshot final
            await page.screenshot(path='step4_final.png', full_page=True)
            
            # Guardar todos los datos encontrados
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            all_data = {
                'timestamp': timestamp,
                'found_emails': found_emails,
                'captured_requests': captured_requests,
                'captured_responses': captured_responses
            }
            
            filename = f"correos_playwright_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, indent=2, ensure_ascii=False)
                
            print(f"\n💾 Datos guardados en: {filename}")
            print(f"📧 Correos encontrados: {len(found_emails)}")
            print(f"📤 Requests capturados: {len(captured_requests)}")
            print(f"📥 Responses capturados: {len(captured_responses)}")
            
            # Mostrar resumen de correos
            if found_emails:
                print(f"\n📧 RESUMEN DE CORREOS:")
                for i, email in enumerate(found_emails, 1):
                    if 'text' in email:
                        print(f"  {i}. {email['text'][:150]}...")
                    elif 'data' in email:
                        print(f"  {i}. Fuente: {email.get('source', 'unknown')} - {len(email['data'])} elementos")
                        
            await browser.close()
            
            return all_data
            
    except ImportError:
        print("❌ Playwright no está disponible")
        print("💡 Instalando Playwright...")
        
        import subprocess
        subprocess.run(['pip', 'install', 'playwright'])
        subprocess.run(['python', '-m', 'playwright', 'install'])
        
        print("✅ Playwright instalado. Ejecuta el script de nuevo.")
        return None
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Función principal"""
    print("🚀 LECTOR DE CORREOS CON PLAYWRIGHT")
    print("=" * 60)
    
    data = await leer_correos_con_playwright()
    
    if data:
        print("\n✅ Lectura completada exitosamente")
        print("📁 Revisa los archivos generados:")
        print("   - correos_playwright_*.json (datos)")
        print("   - step*.png (capturas de pantalla)")
    else:
        print("\n❌ Error en la lectura")
        
    print("\n🏁 Proceso terminado")


if __name__ == "__main__":
    asyncio.run(main())
