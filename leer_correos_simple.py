#!/usr/bin/env python3
"""
Script simple para leer correos usando requests
Hace múltiples intentos y busca en diferentes endpoints
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


class SimpleEmailReader(DujawAPI):
    def __init__(self):
        super().__init__()
        
    def buscar_correos_multiples_intentos(self, max_intentos=5):
        """Hace múltiples intentos para encontrar correos"""
        print(f"🔄 Buscando correos con {max_intentos} intentos...")
        
        correos_encontrados = []
        
        for intento in range(max_intentos):
            print(f"\n🔍 Intento {intento + 1}/{max_intentos}")
            
            # Acceder al mailbox
            response = self.session.get(self.mailbox_url)
            if response.status_code != 200:
                print(f"❌ Error accediendo mailbox: {response.status_code}")
                continue
                
            # Guardar HTML de este intento
            timestamp = datetime.now().strftime("%H%M%S")
            html_file = f"mailbox_intento_{intento+1}_{timestamp}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 HTML guardado: {html_file}")
            
            # Buscar correos en este intento
            correos_intento = self.extraer_correos_del_html(response.text, intento + 1)
            if correos_intento:
                correos_encontrados.extend(correos_intento)
                print(f"✅ Encontrados {len(correos_intento)} correos en intento {intento + 1}")
            else:
                print(f"❌ No se encontraron correos en intento {intento + 1}")
                
            # Esperar antes del siguiente intento
            if intento < max_intentos - 1:
                print("⏳ Esperando 3 segundos...")
                time.sleep(3)
                
        return correos_encontrados
        
    def extraer_correos_del_html(self, html_content, intento_num):
        """Extrae correos del contenido HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')
        correos = []
        
        # Buscar elementos que contengan información de correos
        
        # 1. Buscar elementos con wire:key (Livewire)
        try:
            wire_elements = soup.find_all(attrs=lambda x: x and isinstance(x, dict) and any('wire:' in str(k) for k in x.keys()))
            for element in wire_elements:
                text = element.get_text(strip=True)
                if self.parece_correo(text):
                    correos.append({
                        'tipo': 'wire_element',
                        'intento': intento_num,
                        'contenido': text,
                        'html': str(element)[:300]
                    })
        except Exception as e:
            print(f"Error buscando elementos wire: {e}")
                
        # 2. Buscar en scripts (datos JSON embebidos)
        scripts = soup.find_all('script')
        for script in scripts:
            script_text = script.get_text()
            if 'message' in script_text.lower() or 'email' in script_text.lower():
                # Buscar JSON en el script
                import re
                json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_text)
                for json_str in json_matches:
                    try:
                        data = json.loads(json_str)
                        if self.contiene_info_correo(data):
                            correos.append({
                                'tipo': 'script_json',
                                'intento': intento_num,
                                'contenido': str(data),
                                'data': data
                            })
                    except:
                        continue
                        
        # 3. Buscar tablas que podrían contener correos
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                text = row.get_text(strip=True)
                if self.parece_correo(text):
                    correos.append({
                        'tipo': 'table_row',
                        'intento': intento_num,
                        'contenido': text,
                        'html': str(row)[:300]
                    })
                    
        # 4. Buscar divs con contenido de correo
        divs = soup.find_all('div')
        for div in divs:
            text = div.get_text(strip=True)
            if self.parece_correo(text):
                correos.append({
                    'tipo': 'div_content',
                    'intento': intento_num,
                    'contenido': text,
                    'html': str(div)[:300]
                })
                
        # 5. Buscar elementos con clases específicas
        class_patterns = ['message', 'email', 'mail', 'inbox', 'msg']
        for pattern in class_patterns:
            elements = soup.find_all(class_=lambda x: x and pattern in str(x).lower())
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 20:
                    correos.append({
                        'tipo': f'class_{pattern}',
                        'intento': intento_num,
                        'contenido': text,
                        'html': str(element)[:300]
                    })
                    
        return correos
        
    def parece_correo(self, text):
        """Determina si un texto parece ser un correo"""
        if not text or len(text) < 20:
            return False
            
        # Indicadores de que podría ser un correo
        indicadores = [
            '@' in text,
            'subject' in text.lower(),
            'from' in text.lower(),
            'to' in text.lower(),
            'message' in text.lower(),
            'email' in text.lower(),
            'sender' in text.lower(),
            'recipient' in text.lower(),
            'date' in text.lower(),
            'time' in text.lower()
        ]
        
        return sum(indicadores) >= 2  # Al menos 2 indicadores
        
    def contiene_info_correo(self, data):
        """Verifica si un objeto JSON contiene información de correo"""
        if not isinstance(data, dict):
            return False
            
        data_str = str(data).lower()
        return any(keyword in data_str for keyword in [
            'message', 'email', 'subject', 'from', 'to', 'sender', 'recipient'
        ])
        
    def buscar_endpoints_ajax(self):
        """Busca endpoints AJAX que podrían devolver correos"""
        print("🔍 Buscando endpoints AJAX...")
        
        # Primero acceder al mailbox para activar cualquier JavaScript
        response = self.session.get(self.mailbox_url)
        
        # Buscar en el HTML referencias a endpoints
        soup = BeautifulSoup(response.text, 'html.parser')
        
        endpoints_encontrados = []
        
        # Buscar en scripts
        scripts = soup.find_all('script')
        for script in scripts:
            script_text = script.get_text()
            
            # Buscar URLs en el JavaScript
            import re
            url_patterns = [
                r'["\']([^"\']*(?:message|email|mail|inbox|fetch)[^"\']*)["\']',
                r'url[:\s]*["\']([^"\']+)["\']',
                r'endpoint[:\s]*["\']([^"\']+)["\']'
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, script_text, re.IGNORECASE)
                for match in matches:
                    if match.startswith('/') or match.startswith('http'):
                        endpoints_encontrados.append(match)
                        
        # Probar endpoints encontrados
        for endpoint in set(endpoints_encontrados):
            try:
                if endpoint.startswith('/'):
                    full_url = f"{self.base_url}{endpoint}"
                else:
                    full_url = endpoint
                    
                print(f"🔍 Probando endpoint: {endpoint}")
                resp = self.session.get(full_url)
                
                if resp.status_code == 200:
                    print(f"✅ Endpoint activo: {endpoint}")
                    
                    # Verificar si la respuesta contiene correos
                    if 'json' in resp.headers.get('content-type', '').lower():
                        try:
                            data = resp.json()
                            if self.contiene_info_correo(data):
                                print(f"📧 Endpoint con correos: {endpoint}")
                                return data
                        except:
                            pass
                            
            except Exception as e:
                continue
                
        return None
        
    def mostrar_correos_encontrados(self, correos):
        """Muestra los correos encontrados de forma organizada"""
        print(f"\n📧 CORREOS ENCONTRADOS: {len(correos)}")
        print("=" * 50)
        
        if not correos:
            print("❌ No se encontraron correos")
            return
            
        # Agrupar por tipo
        por_tipo = {}
        for correo in correos:
            tipo = correo['tipo']
            if tipo not in por_tipo:
                por_tipo[tipo] = []
            por_tipo[tipo].append(correo)
            
        for tipo, correos_tipo in por_tipo.items():
            print(f"\n📂 Tipo: {tipo.upper()} ({len(correos_tipo)} correos)")
            print("-" * 30)
            
            for i, correo in enumerate(correos_tipo, 1):
                print(f"\n  📧 Correo {i}:")
                print(f"     Intento: {correo['intento']}")
                print(f"     Contenido: {correo['contenido'][:200]}...")
                
        # Guardar correos
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_simple_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(correos, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Correos guardados en: {filename}")


def main():
    """Función principal"""
    print("🚀 LECTOR SIMPLE DE CORREOS DUJAW.COM")
    print("=" * 60)
    
    reader = SimpleEmailReader()
    
    try:
        # Realizar unlock
        if not reader.unlock_mailbox():
            print("❌ Error en unlock")
            return
            
        print("✅ Unlock exitoso, buscando correos...")
        
        # Buscar endpoints AJAX
        ajax_data = reader.buscar_endpoints_ajax()
        if ajax_data:
            print("✅ Datos encontrados vía AJAX")
            
        # Buscar correos con múltiples intentos
        correos = reader.buscar_correos_multiples_intentos(max_intentos=3)
        
        # Mostrar resultados
        reader.mostrar_correos_encontrados(correos)
        
        if correos:
            print(f"\n🎉 ¡Se encontraron {len(correos)} correos!")
        else:
            print(f"\n😞 No se encontraron correos en ningún intento")
            print("💡 Los correos podrían cargarse dinámicamente vía JavaScript")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        reader.logout()
        
    print("\n🏁 Lectura completada")


if __name__ == "__main__":
    main()
