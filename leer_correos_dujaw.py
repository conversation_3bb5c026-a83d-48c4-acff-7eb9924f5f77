#!/usr/bin/env python3
"""
Script para leer y mostrar todos los correos en dujaw.com
Incluye correos leídos y no leídos
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


class DujawEmailReader(DujawAPI):
    def __init__(self):
        super().__init__()
        
    def buscar_endpoints_email(self):
        """Busca endpoints específicos para obtener emails"""
        print("🔍 Buscando endpoints de email...")
        
        # Endpoints comunes para sistemas de email
        endpoints_to_try = [
            '/api/messages',
            '/api/emails',
            '/api/inbox',
            '/api/mail',
            '/messages',
            '/emails',
            '/inbox',
            '/mail',
            '/fetch-messages',
            '/get-messages',
            '/livewire/message/fetch-messages',
            '/livewire/message/inbox',
            f'/mailbox/{self.mailbox_url.split("/")[-1]}/messages',
            f'/mailbox/{self.mailbox_url.split("/")[-1]}/emails',
        ]
        
        found_endpoints = []
        
        for endpoint in endpoints_to_try:
            full_url = f"{self.base_url}{endpoint}"
            try:
                print(f"🔍 Probando: {endpoint}")
                response = self.session.get(full_url)
                
                if response.status_code == 200:
                    print(f"✅ Endpoint activo: {endpoint}")
                    found_endpoints.append({
                        'url': full_url,
                        'status': response.status_code,
                        'content_type': response.headers.get('content-type', ''),
                        'content_length': len(response.text),
                        'content_preview': response.text[:200]
                    })
                elif response.status_code == 401:
                    print(f"🔐 Requiere auth: {endpoint}")
                elif response.status_code == 403:
                    print(f"🚫 Prohibido: {endpoint}")
                else:
                    print(f"❌ {response.status_code}: {endpoint}")
                    
                time.sleep(0.5)  # Pausa entre requests
                
            except Exception as e:
                print(f"❌ Error en {endpoint}: {e}")
                
        return found_endpoints
        
    def interceptar_llamadas_livewire(self):
        """Intenta interceptar las llamadas de Livewire para obtener mensajes"""
        print("🔄 Intentando interceptar llamadas de Livewire...")
        
        # Primero acceder al mailbox para activar el JavaScript
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print("❌ Error accediendo al mailbox")
            return None
            
        # Buscar en el HTML las llamadas de Livewire
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar scripts que contengan información de Livewire
        scripts = soup.find_all('script')
        livewire_info = {}
        
        for script in scripts:
            script_text = script.get_text()
            if 'livewire' in script_text.lower():
                print("📜 Script de Livewire encontrado")
                
                # Buscar token de Livewire
                import re
                token_match = re.search(r'livewire_token[\'"\s]*[:=][\'"\s]*([a-zA-Z0-9]+)', script_text)
                if token_match:
                    livewire_info['token'] = token_match.group(1)
                    print(f"🔑 Token Livewire: {livewire_info['token'][:20]}...")
                
                # Buscar componentes
                component_match = re.search(r'wire-end:([a-zA-Z0-9]+)', script_text)
                if component_match:
                    livewire_info['component'] = component_match.group(1)
                    print(f"🧩 Componente: {livewire_info['component']}")
                    
        return livewire_info
        
    def intentar_llamada_livewire(self, livewire_info):
        """Intenta hacer una llamada directa a Livewire para obtener mensajes"""
        if not livewire_info or 'token' not in livewire_info:
            print("❌ No hay información de Livewire disponible")
            return None
            
        print("🔄 Intentando llamada directa a Livewire...")
        
        # URLs comunes de Livewire
        livewire_urls = [
            f"{self.base_url}/livewire/message/fetch-messages",
            f"{self.base_url}/livewire/message/inbox",
            f"{self.base_url}/livewire/message/messages",
            f"{self.base_url}/livewire/update",
            f"{self.base_url}/livewire/message"
        ]
        
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml'
        }
        
        # Datos típicos de Livewire
        livewire_data = {
            'fingerprint': {
                'id': livewire_info.get('component', ''),
                'name': 'fetch-messages',
                'locale': 'en',
                'path': self.mailbox_url,
                'method': 'GET'
            },
            'serverMemo': {
                'children': [],
                'errors': [],
                'htmlHash': '',
                'data': {},
                'dataMeta': [],
                'checksum': ''
            },
            'updates': [
                {
                    'type': 'callMethod',
                    'payload': {
                        'method': 'fetchMessages',
                        'params': []
                    }
                }
            ]
        }
        
        for url in livewire_urls:
            try:
                print(f"🔄 Probando Livewire: {url}")
                response = self.session.post(url, json=livewire_data, headers=headers)
                
                if response.status_code == 200:
                    print(f"✅ Respuesta Livewire exitosa: {len(response.text)} chars")
                    try:
                        data = response.json()
                        return data
                    except:
                        print("⚠️ Respuesta no es JSON válido")
                        return response.text
                else:
                    print(f"❌ Error Livewire: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error en llamada Livewire: {e}")
                
        return None
        
    def buscar_mensajes_en_html(self):
        """Busca mensajes directamente en el HTML"""
        print("🔍 Buscando mensajes en HTML...")
        
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            return []
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar elementos que podrían contener mensajes
        potential_messages = []
        
        # Buscar por atributos wire: (Livewire)
        wire_elements = soup.find_all(attrs={'wire:key': True})
        for element in wire_elements:
            text = element.get_text(strip=True)
            if text and len(text) > 20:
                potential_messages.append({
                    'type': 'wire_element',
                    'content': text,
                    'html': str(element)[:300]
                })
                
        # Buscar elementos con clases que sugieran mensajes
        message_classes = ['message', 'email', 'mail', 'inbox-item', 'msg']
        for class_name in message_classes:
            elements = soup.find_all(class_=lambda x: x and class_name in x.lower())
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 20:
                    potential_messages.append({
                        'type': f'class_{class_name}',
                        'content': text,
                        'html': str(element)[:300]
                    })
                    
        # Buscar elementos con IDs que sugieran mensajes
        message_ids = soup.find_all(id=lambda x: x and any(word in x.lower() for word in ['message', 'email', 'mail', 'inbox']))
        for element in message_ids:
            text = element.get_text(strip=True)
            if text and len(text) > 20:
                potential_messages.append({
                    'type': 'id_match',
                    'content': text,
                    'html': str(element)[:300]
                })
                
        return potential_messages
        
    def obtener_todos_los_correos(self):
        """Método principal para obtener todos los correos"""
        print("📧 OBTENIENDO TODOS LOS CORREOS")
        print("=" * 50)
        
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return []
            
        correos_encontrados = []
        
        # Método 1: Buscar endpoints específicos
        print("\n1️⃣ Buscando endpoints de email...")
        endpoints = self.buscar_endpoints_email()
        
        for endpoint in endpoints:
            if 'json' in endpoint['content_type'].lower():
                try:
                    data = json.loads(endpoint['content_preview'])
                    if isinstance(data, list) or 'messages' in str(data).lower():
                        correos_encontrados.append({
                            'source': 'endpoint',
                            'url': endpoint['url'],
                            'data': data
                        })
                except:
                    pass
                    
        # Método 2: Interceptar Livewire
        print("\n2️⃣ Interceptando Livewire...")
        livewire_info = self.interceptar_llamadas_livewire()
        if livewire_info:
            livewire_data = self.intentar_llamada_livewire(livewire_info)
            if livewire_data:
                correos_encontrados.append({
                    'source': 'livewire',
                    'data': livewire_data
                })
                
        # Método 3: Buscar en HTML
        print("\n3️⃣ Buscando en HTML...")
        html_messages = self.buscar_mensajes_en_html()
        if html_messages:
            correos_encontrados.append({
                'source': 'html',
                'data': html_messages
            })
            
        return correos_encontrados
        
    def mostrar_correos(self, correos):
        """Muestra los correos encontrados de forma organizada"""
        print("\n📧 CORREOS ENCONTRADOS")
        print("=" * 50)
        
        if not correos:
            print("❌ No se encontraron correos")
            return
            
        for i, correo_group in enumerate(correos, 1):
            print(f"\n📂 Fuente {i}: {correo_group['source'].upper()}")
            print("-" * 30)
            
            data = correo_group['data']
            
            if correo_group['source'] == 'html':
                for j, msg in enumerate(data, 1):
                    print(f"\n  📧 Mensaje {j} ({msg['type']}):")
                    print(f"     Contenido: {msg['content'][:200]}...")
                    
            elif correo_group['source'] == 'livewire':
                print(f"     Datos Livewire: {str(data)[:300]}...")
                
            elif correo_group['source'] == 'endpoint':
                print(f"     URL: {correo_group['url']}")
                print(f"     Datos: {str(data)[:300]}...")
                
        # Guardar todos los correos en un archivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_encontrados_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(correos, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Correos guardados en: {filename}")


def main():
    """Función principal"""
    print("🚀 LECTOR DE CORREOS DUJAW.COM")
    print("=" * 50)
    
    reader = DujawEmailReader()
    
    try:
        # Obtener todos los correos
        correos = reader.obtener_todos_los_correos()
        
        # Mostrar los correos
        reader.mostrar_correos(correos)
        
        print(f"\n📊 RESUMEN:")
        print(f"   Fuentes exploradas: {len(correos)}")
        
        total_messages = 0
        for correo_group in correos:
            if correo_group['source'] == 'html':
                total_messages += len(correo_group['data'])
                
        print(f"   Mensajes potenciales: {total_messages}")
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        reader.logout()
        
    print("\n🏁 Lectura de correos completada")


if __name__ == "__main__":
    main()
