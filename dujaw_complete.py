#!/usr/bin/env python3
"""
Automatización completa para dujaw.com
Basado en el análisis del HTML real del sitio
"""

import requests
import json
import time
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup


class DujawCompleteAutomation:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://dujaw.com/'
        })
        
        self.network_requests = []
        self.base_url = "https://dujaw.com"
        self.target_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.unlock_url = "https://dujaw.com/unlock"
        self.password = "unlockgs2024"
        
    def log_request(self, method, url, headers=None, data=None, response=None):
        """Registra una petición HTTP"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'method': method,
            'url': url,
            'headers': dict(headers) if headers else {},
            'post_data': data,
            'response_status': response.status_code if response else None,
            'response_headers': dict(response.headers) if response else {},
            'response_content': response.text if response and response.text else None,
            'resource_type': 'xhr' if '/api/' in url or url.endswith('.json') else 'document'
        }
        self.network_requests.append(log_entry)
        print(f"📤 {method} {url} -> {response.status_code if response else 'N/A'}")
        
    def get_page(self, url, **kwargs):
        """Realiza una petición GET"""
        try:
            response = self.session.get(url, **kwargs)
            self.log_request('GET', url, self.session.headers, None, response)
            return response
        except Exception as e:
            print(f"❌ Error en GET {url}: {e}")
            return None
            
    def post_data(self, url, data=None, json_data=None, **kwargs):
        """Realiza una petición POST"""
        try:
            if json_data:
                response = self.session.post(url, json=json_data, **kwargs)
                self.log_request('POST', url, self.session.headers, json_data, response)
            else:
                response = self.session.post(url, data=data, **kwargs)
                self.log_request('POST', url, self.session.headers, data, response)
            return response
        except Exception as e:
            print(f"❌ Error en POST {url}: {e}")
            return None
            
    def step1_get_unlock_page(self):
        """Paso 1: Obtener la página de unlock"""
        print("🔓 Paso 1: Obteniendo página de unlock...")
        
        response = self.get_page(self.target_url)
        if not response:
            return None, None
            
        print(f"✅ Página obtenida: {response.status_code}")
        
        # Parsear HTML
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            return response, soup
        except Exception as e:
            print(f"❌ Error parseando HTML: {e}")
            return response, None
            
    def step2_extract_csrf_token(self, soup):
        """Paso 2: Extraer el token CSRF"""
        print("🔑 Paso 2: Extrayendo token CSRF...")
        
        if not soup:
            return None
            
        # Buscar el token CSRF en el input hidden
        csrf_input = soup.find('input', {'name': '_token'})
        if csrf_input:
            token = csrf_input.get('value')
            print(f"✅ Token CSRF encontrado: {token[:20]}...")
            return token
        else:
            print("❌ No se encontró token CSRF")
            return None
            
    def step3_perform_unlock(self, csrf_token):
        """Paso 3: Realizar el unlock"""
        print("🔐 Paso 3: Realizando unlock...")
        
        # Datos del formulario basados en el HTML analizado
        form_data = {
            '_token': csrf_token,
            'password': self.password
        }
        
        print(f"📋 Enviando datos: {list(form_data.keys())}")
        
        # Enviar POST al endpoint de unlock
        response = self.post_data(self.unlock_url, data=form_data)
        
        if response:
            print(f"✅ Unlock enviado: {response.status_code}")
            
            # Verificar si fue exitoso
            if response.status_code == 200:
                print("🎉 Unlock exitoso (200)")
                return response
            elif response.status_code == 302:
                print("🎉 Unlock exitoso (302 - redirect)")
                # Seguir el redirect
                if 'location' in response.headers:
                    redirect_url = response.headers['location']
                    print(f"🔄 Siguiendo redirect a: {redirect_url}")
                    return self.get_page(redirect_url)
                return response
            else:
                print(f"⚠️ Respuesta inesperada: {response.status_code}")
                return response
        else:
            print("❌ Error en unlock")
            return None
            
    def step4_explore_mailbox(self):
        """Paso 4: Explorar el mailbox después del unlock"""
        print("📧 Paso 4: Explorando mailbox...")
        
        # Intentar acceder al mailbox original
        response = self.get_page(self.target_url)
        if not response:
            return
            
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Buscar elementos de la interfaz del mailbox
            print("🔍 Analizando interfaz del mailbox...")
            
            # Buscar enlaces y botones
            links = soup.find_all('a', href=True)
            buttons = soup.find_all('button')
            forms = soup.find_all('form')
            
            print(f"🔗 Enlaces encontrados: {len(links)}")
            print(f"🔘 Botones encontrados: {len(buttons)}")
            print(f"📋 Formularios encontrados: {len(forms)}")
            
            # Buscar elementos específicos del mailbox
            mailbox_elements = []
            
            # Buscar por texto relevante
            relevant_texts = ['inbox', 'mail', 'message', 'compose', 'send', 'delete', 'refresh', 'new']
            
            for link in links:
                text = link.get_text(strip=True).lower()
                href = link.get('href', '')
                
                if any(keyword in text or keyword in href.lower() for keyword in relevant_texts):
                    mailbox_elements.append({
                        'type': 'link',
                        'text': link.get_text(strip=True),
                        'href': href,
                        'full_url': urljoin(self.target_url, href)
                    })
                    
            for button in buttons:
                text = button.get_text(strip=True).lower()
                onclick = button.get('onclick', '')
                
                if any(keyword in text or keyword in onclick.lower() for keyword in relevant_texts):
                    mailbox_elements.append({
                        'type': 'button',
                        'text': button.get_text(strip=True),
                        'onclick': onclick
                    })
                    
            print(f"🎯 Elementos del mailbox encontrados: {len(mailbox_elements)}")
            
            # Mostrar elementos encontrados
            for elem in mailbox_elements:
                if elem['type'] == 'link':
                    print(f"   🔗 {elem['text']} -> {elem['href']}")
                else:
                    print(f"   🔘 {elem['text']} (onclick: {elem['onclick'][:50]}...)")
                    
            # Visitar enlaces relevantes
            for elem in mailbox_elements[:5]:  # Solo los primeros 5
                if elem['type'] == 'link' and elem['href']:
                    print(f"🔗 Visitando: {elem['text']} -> {elem['full_url']}")
                    self.get_page(elem['full_url'])
                    time.sleep(1)
                    
        except Exception as e:
            print(f"❌ Error explorando mailbox: {e}")
            
    def step5_search_api_endpoints(self):
        """Paso 5: Buscar endpoints de API"""
        print("🔍 Paso 5: Buscando endpoints de API...")
        
        # Endpoints comunes para mailbox/email
        api_endpoints = [
            '/api/mail',
            '/api/mailbox',
            '/api/inbox',
            '/api/messages',
            '/api/emails',
            '/api/compose',
            '/api/send',
            '/api/delete',
            '/api/refresh',
            '/ajax/mail',
            '/ajax/mailbox',
            '/ajax/inbox',
            '/ajax/messages',
            '/mail.json',
            '/inbox.json',
            '/messages.json',
            '/mailbox.json'
        ]
        
        found_endpoints = []
        
        for endpoint in api_endpoints:
            full_url = urljoin(self.base_url, endpoint)
            print(f"🔍 Probando: {full_url}")
            
            response = self.get_page(full_url)
            
            if response:
                if response.status_code == 200:
                    print(f"✅ Endpoint activo: {full_url}")
                    found_endpoints.append(full_url)
                elif response.status_code == 401:
                    print(f"🔐 Endpoint requiere auth: {full_url}")
                    found_endpoints.append(full_url)
                elif response.status_code == 403:
                    print(f"🚫 Endpoint prohibido: {full_url}")
                elif response.status_code == 404:
                    print(f"❌ Endpoint no existe: {full_url}")
                else:
                    print(f"⚠️ Respuesta inesperada {response.status_code}: {full_url}")
                    
            time.sleep(0.5)  # Pausa entre requests
            
        print(f"\n🎯 Endpoints encontrados: {len(found_endpoints)}")
        return found_endpoints
        
    def save_captured_data(self):
        """Guarda los datos capturados"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Guardar requests
        requests_file = f"network_requests_{timestamp}.json"
        with open(requests_file, 'w', encoding='utf-8') as f:
            json.dump(self.network_requests, f, indent=2, ensure_ascii=False)
            
        # Crear archivo de responses
        responses_file = f"network_responses_{timestamp}.json"
        responses_data = []
        for req in self.network_requests:
            if req.get('response_content'):
                response_entry = {
                    'timestamp': req['timestamp'],
                    'status': req['response_status'],
                    'url': req['url'],
                    'headers': req['response_headers'],
                    'content': req['response_content'][:2000] if len(req['response_content']) > 2000 else req['response_content'],
                    'resource_type': req['resource_type']
                }
                responses_data.append(response_entry)
                
        with open(responses_file, 'w', encoding='utf-8') as f:
            json.dump(responses_data, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Datos guardados:")
        print(f"   📤 Requests: {requests_file} ({len(self.network_requests)} entradas)")
        print(f"   📥 Responses: {responses_file} ({len(responses_data)} entradas)")
        
        return requests_file, responses_file
        
    def analyze_captured_data(self):
        """Analiza los datos capturados"""
        print(f"\n🔍 ANÁLISIS FINAL:")
        print("=" * 40)
        
        total_requests = len(self.network_requests)
        print(f"📊 Total de requests: {total_requests}")
        
        # Agrupar por método
        methods = {}
        status_codes = {}
        
        for req in self.network_requests:
            method = req['method']
            status = req.get('response_status', 'N/A')
            
            methods[method] = methods.get(method, 0) + 1
            status_codes[status] = status_codes.get(status, 0) + 1
            
        print("\n📈 Métodos HTTP:")
        for method, count in methods.items():
            print(f"   {method}: {count}")
            
        print("\n📊 Códigos de respuesta:")
        for status, count in status_codes.items():
            print(f"   {status}: {count}")
            
        # Buscar APIs potenciales
        api_requests = [req for req in self.network_requests if req['resource_type'] == 'xhr' or '/api/' in req['url']]
        print(f"\n🔗 APIs detectadas: {len(api_requests)}")
        
        for api in api_requests:
            print(f"   {api['method']} {api['url']} -> {api.get('response_status', 'N/A')}")
            
        return len(api_requests) > 0


def main():
    """Función principal"""
    print("🚀 AUTOMATIZACIÓN COMPLETA DUJAW.COM")
    print("=" * 50)
    print("URL: https://dujaw.com/mailbox/<EMAIL>")
    print("Password: unlockgs2024")
    print("=" * 50)
    
    automation = DujawCompleteAutomation()
    
    try:
        # Paso 1: Obtener página de unlock
        response, soup = automation.step1_get_unlock_page()
        if not response:
            print("❌ No se pudo obtener la página inicial")
            return
            
        # Paso 2: Extraer token CSRF
        csrf_token = automation.step2_extract_csrf_token(soup)
        if not csrf_token:
            print("❌ No se pudo obtener el token CSRF")
            return
            
        # Paso 3: Realizar unlock
        unlock_response = automation.step3_perform_unlock(csrf_token)
        if not unlock_response:
            print("❌ Error en el proceso de unlock")
            return
            
        print("✅ Unlock completado, procediendo a explorar...")
        
        # Paso 4: Explorar mailbox
        automation.step4_explore_mailbox()
        
        # Paso 5: Buscar APIs
        found_endpoints = automation.step5_search_api_endpoints()
        
        # Analizar y guardar datos
        has_apis = automation.analyze_captured_data()
        requests_file, responses_file = automation.save_captured_data()
        
        print(f"\n✅ AUTOMATIZACIÓN COMPLETADA")
        print(f"📁 Archivos generados: {requests_file}, {responses_file}")
        print(f"🎯 Endpoints encontrados: {len(found_endpoints)}")
        
        if has_apis or found_endpoints:
            print("\n🔄 Ejecuta 'python api_generator.py' para generar código API")
        else:
            print("\n💡 Se capturaron datos del flujo web para análisis manual")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    print("\n🏁 Proceso terminado")


if __name__ == "__main__":
    main()
