#!/usr/bin/env python3
"""
Script simple para probar dujaw.com
"""

import requests
import json
from datetime import datetime

def test_dujaw():
    print("🚀 Probando dujaw.com...")
    
    url = "https://dujaw.com/mailbox/<EMAIL>"
    password = "unlockgs2024"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        print(f"📡 Conectando a: {url}")
        response = session.get(url, timeout=30)
        print(f"✅ Respuesta: {response.status_code}")
        print(f"📄 Contenido (primeros 500 chars):")
        print(response.text[:500])
        print("=" * 50)
        
        # Guardar respuesta para análisis
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dujaw_response_{timestamp}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"💾 Respuesta guardada en: {filename}")
        
        # Buscar formularios
        if 'form' in response.text.lower():
            print("✅ Se detectaron formularios en la página")
        else:
            print("❌ No se detectaron formularios")
            
        # Buscar campos de contraseña
        if 'password' in response.text.lower():
            print("✅ Se detectó campo de contraseña")
        else:
            print("❌ No se detectó campo de contraseña")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_dujaw()
