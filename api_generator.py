#!/usr/bin/env python3
"""
Generador de código API basado en los datos capturados del flujo web
Analiza las llamadas de red y genera código Python para replicar las acciones vía API
"""

import json
import re
import urllib.parse
from typing import List, Dict, Any
from datetime import datetime


class APIGenerator:
    def __init__(self):
        self.session_cookies = {}
        self.auth_headers = {}
        self.base_url = ""
        self.api_endpoints = []
        
    def load_network_data(self, requests_file: str, responses_file: str):
        """Carga los datos de red capturados"""
        try:
            with open(requests_file, 'r', encoding='utf-8') as f:
                self.requests = json.load(f)
            with open(responses_file, 'r', encoding='utf-8') as f:
                self.responses = json.load(f)
            print(f"✅ Datos cargados: {len(self.requests)} requests, {len(self.responses)} responses")
        except Exception as e:
            print(f"❌ Error cargando datos: {e}")
            
    def analyze_authentication(self):
        """Analiza el proceso de autenticación"""
        print("\n🔐 ANÁLISIS DE AUTENTICACIÓN:")
        print("=" * 40)
        
        auth_requests = []
        for req in self.requests:
            url_lower = req['url'].lower()
            if any(keyword in url_lower for keyword in ['login', 'auth', 'signin', 'session']):
                auth_requests.append(req)
                
        for req in auth_requests:
            print(f"🔗 {req['method']} {req['url']}")
            if req['post_data']:
                print(f"   📤 Data: {req['post_data']}")
            print(f"   📋 Headers: {json.dumps(req['headers'], indent=4)}")
            print()
            
        # Buscar cookies de sesión en las respuestas
        for resp in self.responses:
            if 'set-cookie' in resp['headers']:
                cookies = resp['headers']['set-cookie']
                print(f"🍪 Cookies encontradas en {resp['url']}: {cookies}")
                
    def extract_api_endpoints(self):
        """Extrae los endpoints de API relevantes"""
        print("\n🔍 EXTRAYENDO ENDPOINTS DE API:")
        print("=" * 40)
        
        api_patterns = [
            r'/api/',
            r'/v\d+/',
            r'\.json',
            r'/ajax/',
            r'/rest/',
            r'/graphql'
        ]
        
        for req in self.requests:
            if req['resource_type'] in ['xhr', 'fetch']:
                # Es una llamada AJAX/API
                endpoint = {
                    'method': req['method'],
                    'url': req['url'],
                    'headers': req['headers'],
                    'data': req['post_data'],
                    'timestamp': req['timestamp']
                }
                self.api_endpoints.append(endpoint)
                print(f"🔗 {req['method']} {req['url']}")
                
        print(f"\n📊 Total endpoints encontrados: {len(self.api_endpoints)}")
        
    def generate_python_code(self):
        """Genera código Python para replicar las llamadas de API"""
        code = '''#!/usr/bin/env python3
"""
Código generado automáticamente para replicar el flujo web vía API
Basado en el análisis de llamadas de red capturadas
"""

import requests
import json
from typing import Dict, Any, Optional


class DomainEmailAPI:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
            'Content-Type': 'application/json'
        })
        
    def login(self, username: str, password: str) -> bool:
        """Realiza el login y obtiene las cookies/tokens de sesión"""
        try:
'''
        
        # Buscar endpoint de login
        login_endpoint = None
        for endpoint in self.api_endpoints:
            url_lower = endpoint['url'].lower()
            if any(keyword in url_lower for keyword in ['login', 'auth', 'signin']) and endpoint['method'] == 'POST':
                login_endpoint = endpoint
                break
                
        if login_endpoint:
            parsed_url = urllib.parse.urlparse(login_endpoint['url'])
            path = parsed_url.path
            
            code += f'''
            # Endpoint de login detectado: {login_endpoint['url']}
            login_data = {{
                'username': username,  # Ajustar según el campo real
                'password': password   # Ajustar según el campo real
            }}
            
            response = self.session.post(
                f"{{self.base_url}}{path}",
                json=login_data
            )
            
            if response.status_code == 200:
                print("✅ Login exitoso")
                return True
            else:
                print(f"❌ Error en login: {{response.status_code}} - {{response.text}}")
                return False
                
        except Exception as e:
            print(f"❌ Error en login: {{e}}")
            return False
'''
        else:
            code += '''
            # No se detectó endpoint de login automáticamente
            # Implementar manualmente basado en el análisis de red
            pass
'''
        
        # Generar métodos para otros endpoints
        for i, endpoint in enumerate(self.api_endpoints):
            if 'login' not in endpoint['url'].lower():
                parsed_url = urllib.parse.urlparse(endpoint['url'])
                path = parsed_url.path
                method_name = f"api_call_{i+1}"
                
                code += f'''
    
    def {method_name}(self, **kwargs) -> Dict[str, Any]:
        """
        Llamada API: {endpoint['method']} {endpoint['url']}
        Timestamp original: {endpoint['timestamp']}
        """
        try:
'''
                
                if endpoint['method'] == 'GET':
                    code += f'''
            response = self.session.get(
                f"{{self.base_url}}{path}",
                params=kwargs
            )
'''
                else:
                    code += f'''
            response = self.session.{endpoint['method'].lower()}(
                f"{{self.base_url}}{path}",
                json=kwargs
            )
'''
                
                code += '''
            response.raise_for_status()
            return response.json() if response.content else {}
            
        except Exception as e:
            print(f"❌ Error en API call: {e}")
            return {}
'''
        
        # Agregar método principal de ejemplo
        code += '''

    def manage_domains(self):
        """Método principal para gestionar dominios - personalizar según necesidades"""
        if not self.login("tu_usuario", "tu_password"):
            return False
            
        # Ejemplo de uso de los endpoints detectados
        # Personalizar según las acciones específicas que necesites
        
        # Llamar a los endpoints en el orden detectado
'''
        
        for i in range(len(self.api_endpoints)):
            if 'login' not in self.api_endpoints[i]['url'].lower():
                code += f'''
        result_{i+1} = self.api_call_{i+1}()
        print(f"Resultado {i+1}: {{result_{i+1}}}")
'''
        
        code += '''
        
        return True


# Ejemplo de uso
if __name__ == "__main__":
    # Configurar la URL base (extraer del análisis)
    api = DomainEmailAPI("https://tu-sitio-web.com")
    
    # Ejecutar el flujo de gestión
    api.manage_domains()
'''
        
        return code
        
    def save_generated_code(self, code: str):
        """Guarda el código generado"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"generated_api_{timestamp}.py"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(code)
            
        print(f"💾 Código generado guardado en: {filename}")
        
    def generate_analysis_report(self):
        """Genera un reporte de análisis"""
        report = f"""
# REPORTE DE ANÁLISIS DE API
Generado: {datetime.now().isoformat()}

## Resumen
- Total de requests capturados: {len(self.requests)}
- Total de responses capturados: {len(self.responses)}
- Endpoints de API detectados: {len(self.api_endpoints)}

## Endpoints Detectados
"""
        
        for i, endpoint in enumerate(self.api_endpoints, 1):
            report += f"""
### {i}. {endpoint['method']} {endpoint['url']}
- Timestamp: {endpoint['timestamp']}
- Headers: {json.dumps(endpoint['headers'], indent=2)}
"""
            if endpoint['data']:
                report += f"- Data: {endpoint['data']}\n"
                
        report += """

## Recomendaciones
1. Revisar el código generado y ajustar los nombres de campos según la API real
2. Implementar manejo de errores específico para cada endpoint
3. Agregar validación de datos de entrada
4. Considerar implementar rate limiting si es necesario
5. Testear cada endpoint individualmente antes de usar en producción

## Próximos Pasos
1. Ejecutar el código generado con datos de prueba
2. Ajustar los parámetros según los resultados
3. Implementar la lógica de negocio específica
4. Agregar logging y monitoreo
"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"analysis_report_{timestamp}.md"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
            
        print(f"📊 Reporte de análisis guardado en: {filename}")


def main():
    """Función principal"""
    print("🔧 Generador de API basado en captura web")
    
    # Buscar archivos de datos más recientes
    import glob
    import os
    
    request_files = glob.glob("network_requests_*.json")
    response_files = glob.glob("network_responses_*.json")
    
    if not request_files or not response_files:
        print("❌ No se encontraron archivos de datos de red.")
        print("   Ejecuta primero web_automation.py para capturar los datos.")
        return
        
    # Usar los archivos más recientes
    latest_request = max(request_files, key=os.path.getctime)
    latest_response = max(response_files, key=os.path.getctime)
    
    print(f"📂 Usando archivos: {latest_request}, {latest_response}")
    
    # Generar API
    generator = APIGenerator()
    generator.load_network_data(latest_request, latest_response)
    generator.analyze_authentication()
    generator.extract_api_endpoints()
    
    # Generar código
    code = generator.generate_python_code()
    generator.save_generated_code(code)
    generator.generate_analysis_report()
    
    print("\n✅ Generación completada!")
    print("📋 Revisa los archivos generados y ajusta según tus necesidades.")


if __name__ == "__main__":
    main()
