#!/usr/bin/env python3
"""
Automatización para dujaw.com usando requests
Versión simplificada que funciona sin dependencias complejas
"""

import requests
import json
import time
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup


class DujawRequestsAutomation:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.network_requests = []
        self.base_url = "https://dujaw.com"
        self.target_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.password = "unlockgs2024"
        
    def log_request(self, method, url, headers=None, data=None, response=None):
        """Registra una petición HTTP"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'method': method,
            'url': url,
            'headers': dict(headers) if headers else {},
            'post_data': data,
            'response_status': response.status_code if response else None,
            'response_headers': dict(response.headers) if response else {},
            'response_content': response.text if response and response.text else None,
            'resource_type': 'xhr' if '/api/' in url or url.endswith('.json') else 'document'
        }
        self.network_requests.append(log_entry)
        print(f"📤 {method} {url} -> {response.status_code if response else 'N/A'}")
        
    def get_page(self, url, **kwargs):
        """Realiza una petición GET"""
        try:
            response = self.session.get(url, **kwargs)
            self.log_request('GET', url, self.session.headers, None, response)
            return response
        except Exception as e:
            print(f"❌ Error en GET {url}: {e}")
            return None
            
    def post_data(self, url, data=None, json_data=None, **kwargs):
        """Realiza una petición POST"""
        try:
            if json_data:
                response = self.session.post(url, json=json_data, **kwargs)
                self.log_request('POST', url, self.session.headers, json_data, response)
            else:
                response = self.session.post(url, data=data, **kwargs)
                self.log_request('POST', url, self.session.headers, data, response)
            return response
        except Exception as e:
            print(f"❌ Error en POST {url}: {e}")
            return None
            
    def navigate_to_mailbox(self):
        """Navega a la URL del mailbox"""
        print(f"🌐 Navegando a: {self.target_url}")
        
        response = self.get_page(self.target_url)
        if not response:
            return None, None
            
        print(f"✅ Página cargada: {response.status_code}")
        
        # Parsear HTML
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            return response, soup
        except Exception as e:
            print(f"❌ Error parseando HTML: {e}")
            return response, None
            
    def find_login_form(self, soup):
        """Encuentra el formulario de login"""
        if not soup:
            return None
            
        print("🔍 Buscando formulario de login...")
        
        # Buscar formularios
        forms = soup.find_all('form')
        print(f"📋 Encontrados {len(forms)} formularios")
        
        # Buscar campos de contraseña
        password_inputs = soup.find_all('input', {'type': 'password'})
        if not password_inputs:
            # Buscar por name o id
            password_inputs = soup.find_all('input', {'name': re.compile(r'pass', re.I)})
            if not password_inputs:
                password_inputs = soup.find_all('input', {'id': re.compile(r'pass', re.I)})
                
        print(f"🔒 Encontrados {len(password_inputs)} campos de contraseña")
        
        if password_inputs:
            # Encontrar el formulario que contiene el campo de contraseña
            password_field = password_inputs[0]
            form = password_field.find_parent('form')
            
            if form:
                print("✅ Formulario de login encontrado")
                return form, password_field
            else:
                print("⚠️ Campo de contraseña encontrado pero sin formulario padre")
                return None, password_field
                
        print("❌ No se encontró formulario de login")
        return None, None
        
    def extract_form_data(self, form, soup):
        """Extrae datos del formulario"""
        form_data = {}
        
        if form:
            # Buscar todos los inputs en el formulario
            inputs = form.find_all('input')
        else:
            # Si no hay formulario, buscar todos los inputs en la página
            inputs = soup.find_all('input')
            
        for input_field in inputs:
            name = input_field.get('name')
            value = input_field.get('value', '')
            input_type = input_field.get('type', 'text')
            
            if name:
                if input_type == 'password':
                    form_data[name] = self.password
                    print(f"🔒 Campo contraseña: {name}")
                elif input_type in ['hidden', 'text', 'email']:
                    form_data[name] = value
                    print(f"📝 Campo {input_type}: {name} = {value}")
                    
        return form_data
        
    def get_form_action(self, form, current_url):
        """Obtiene la URL de acción del formulario"""
        if not form:
            return current_url
            
        action = form.get('action', '')
        if not action:
            return current_url
        elif action.startswith('http'):
            return action
        else:
            return urljoin(current_url, action)
            
    def attempt_login(self):
        """Intenta realizar el login"""
        print("\n🔐 Iniciando proceso de login...")
        
        # Navegar a la página
        response, soup = self.navigate_to_mailbox()
        if not response:
            return False
            
        # Buscar formulario de login
        form, password_field = self.find_login_form(soup)
        
        # Extraer datos del formulario
        form_data = self.extract_form_data(form, soup)
        
        if not form_data:
            print("❌ No se pudieron extraer datos del formulario")
            return False
            
        print(f"📋 Datos del formulario: {list(form_data.keys())}")
        
        # Determinar URL de envío
        form_action = self.get_form_action(form, self.target_url)
        print(f"🎯 Enviando a: {form_action}")
        
        # Enviar formulario
        login_response = self.post_data(form_action, data=form_data)
        
        if login_response:
            print(f"✅ Login enviado: {login_response.status_code}")
            
            # Verificar si el login fue exitoso
            if login_response.status_code in [200, 302]:
                print("🎉 Login aparentemente exitoso")
                return True
            else:
                print(f"⚠️ Respuesta inesperada: {login_response.status_code}")
                
        return False
        
    def explore_mailbox_features(self):
        """Explora las funcionalidades del mailbox"""
        print("\n📧 Explorando funcionalidades del mailbox...")
        
        # Intentar acceder a la página principal después del login
        response = self.get_page(self.target_url)
        if not response:
            return
            
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Buscar enlaces y botones interactivos
            links = soup.find_all('a', href=True)
            buttons = soup.find_all('button')
            
            print(f"🔗 Encontrados {len(links)} enlaces")
            print(f"🔘 Encontrados {len(buttons)} botones")
            
            # Filtrar enlaces relevantes
            relevant_links = []
            keywords = ['mail', 'inbox', 'compose', 'send', 'delete', 'api', 'ajax']
            
            for link in links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if any(keyword in href.lower() or keyword in text.lower() for keyword in keywords):
                    relevant_links.append({
                        'href': href,
                        'text': text,
                        'full_url': urljoin(self.target_url, href)
                    })
                    
            print(f"🎯 Enlaces relevantes encontrados: {len(relevant_links)}")
            
            # Visitar algunos enlaces relevantes
            for link in relevant_links[:5]:  # Solo los primeros 5
                print(f"🔗 Visitando: {link['text']} -> {link['full_url']}")
                self.get_page(link['full_url'])
                time.sleep(1)  # Pausa entre requests
                
        except Exception as e:
            print(f"❌ Error explorando funcionalidades: {e}")
            
    def search_for_apis(self):
        """Busca posibles endpoints de API"""
        print("\n🔍 Buscando endpoints de API...")
        
        # URLs comunes de API para probar
        api_endpoints = [
            '/api/mail',
            '/api/inbox',
            '/api/messages',
            '/api/compose',
            '/api/send',
            '/api/delete',
            '/ajax/mail',
            '/ajax/inbox',
            '/mail.json',
            '/inbox.json'
        ]
        
        for endpoint in api_endpoints:
            full_url = urljoin(self.base_url, endpoint)
            print(f"🔍 Probando: {full_url}")
            response = self.get_page(full_url)
            
            if response and response.status_code == 200:
                print(f"✅ Endpoint activo: {full_url}")
            
            time.sleep(0.5)  # Pausa entre requests
            
    def save_captured_data(self):
        """Guarda los datos capturados"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Guardar requests
        requests_file = f"network_requests_{timestamp}.json"
        with open(requests_file, 'w', encoding='utf-8') as f:
            json.dump(self.network_requests, f, indent=2, ensure_ascii=False)
            
        # Crear archivo de responses vacío para compatibilidad
        responses_file = f"network_responses_{timestamp}.json"
        responses_data = []
        for req in self.network_requests:
            if req.get('response_content'):
                response_entry = {
                    'timestamp': req['timestamp'],
                    'status': req['response_status'],
                    'url': req['url'],
                    'headers': req['response_headers'],
                    'content': req['response_content'][:1000],  # Limitar contenido
                    'resource_type': req['resource_type']
                }
                responses_data.append(response_entry)
                
        with open(responses_file, 'w', encoding='utf-8') as f:
            json.dump(responses_data, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Datos guardados:")
        print(f"   📤 Requests: {requests_file} ({len(self.network_requests)} entradas)")
        print(f"   📥 Responses: {responses_file} ({len(responses_data)} entradas)")
        
        return requests_file, responses_file
        
    def analyze_captured_data(self):
        """Analiza los datos capturados"""
        print(f"\n🔍 ANÁLISIS DE DATOS CAPTURADOS:")
        print("=" * 40)
        
        total_requests = len(self.network_requests)
        print(f"📊 Total de requests: {total_requests}")
        
        # Agrupar por método
        methods = {}
        for req in self.network_requests:
            method = req['method']
            methods[method] = methods.get(method, 0) + 1
            
        print("\n📈 Métodos HTTP:")
        for method, count in methods.items():
            print(f"   {method}: {count}")
            
        # Buscar APIs potenciales
        api_requests = [req for req in self.network_requests if req['resource_type'] == 'xhr']
        print(f"\n🔗 Posibles APIs detectadas: {len(api_requests)}")
        
        for api in api_requests:
            print(f"   {api['method']} {api['url']}")
            
        return len(api_requests) > 0


def main():
    """Función principal"""
    print("🚀 AUTOMATIZACIÓN DUJAW.COM (REQUESTS)")
    print("=" * 50)
    print("URL: https://dujaw.com/mailbox/<EMAIL>")
    print("Password: unlockgs2024")
    print("=" * 50)
    
    automation = DujawRequestsAutomation()
    
    try:
        # Intentar login
        login_success = automation.attempt_login()
        
        if login_success:
            print("\n✅ Procediendo a explorar funcionalidades...")
            
            # Explorar funcionalidades
            automation.explore_mailbox_features()
            
            # Buscar APIs
            automation.search_for_apis()
        else:
            print("\n⚠️ Login no confirmado, pero continuando con exploración...")
            automation.explore_mailbox_features()
            
        # Analizar y guardar datos
        has_apis = automation.analyze_captured_data()
        requests_file, responses_file = automation.save_captured_data()
        
        print(f"\n✅ CAPTURA COMPLETADA")
        print(f"📁 Archivos generados: {requests_file}, {responses_file}")
        
        if has_apis:
            print("\n🔄 Ejecuta 'python api_generator.py' para generar código API")
        else:
            print("\n💡 No se detectaron APIs específicas, pero los datos están disponibles para análisis")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    print("\n🏁 Proceso terminado")


if __name__ == "__main__":
    main()
