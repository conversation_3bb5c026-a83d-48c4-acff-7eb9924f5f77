<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <title>Dujaw Store</title>
        
        <link rel="icon" href="https://dujaw.com/storage/public/images/joystick.png">
        <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" onload="this.onload=null;this.rel='stylesheet'" />
    <link rel="preload" as="style" href="https://dujaw.com/css/vendor.css" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="stylesheet" href="https://dujaw.com/css/common.css">
    <script src="https://dujaw.com/vendor/Shortcode/Shortcode.js"></script>
    <script src="https://dujaw.com/js/app.js" defer></script>
    <style >[wire\:loading], [wire\:loading\.delay], [wire\:loading\.inline-block], [wire\:loading\.inline], [wire\:loading\.block], [wire\:loading\.flex], [wire\:loading\.table], [wire\:loading\.grid], [wire\:loading\.inline-flex] {display: none;}[wire\:loading\.delay\.shortest], [wire\:loading\.delay\.shorter], [wire\:loading\.delay\.short], [wire\:loading\.delay\.long], [wire\:loading\.delay\.longer], [wire\:loading\.delay\.longest] {display:none;}[wire\:offline] {display: none;}[wire\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }</style>
    
        
        <meta name="csrf-token" content="nUSJbGWH46tyUOVWwPxj40E0Wy4AcLdb3IzjYBaA">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Kadwa:wght@400;600;700&display=swap" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
<style>
:root {
  --head-font: "Kadwa";
  --body-font: "Poppins";
  --primary: #0155b5;
  --secondary: #2fc10a;
  --tertiary: #d2ab3e;
}
</style>
<script>
  let captcha_name = "off";
  let site_key = "";
  if(captcha_name && captcha_name !== "off") {
    site_key = "";
  }
  let strings = {"Get back to MailBox":"Get back to MailBox","Enter Username":"Enter Username","Select Domain":"Select Domain","Create":"Create","Random":"Random","Custom":"Custom","Menu":"Menu","Cancel":"Cancel","Copy":"Copy","Refresh":"Refresh","New":"New","Delete":"Delete","Download":"Download","Fetching":"Fetching","Empty Inbox":"Empty Inbox","Error":"Error","Success":"Success","Close":"Close","Email ID Copied to Clipboard":"Email ID Copied to Clipboard","Please enter Username":"Please enter Username","Please Select a Domain":"Please Select a Domain","Username not allowed":"Username not allowed","Your Temporary Email Address":"Your Temporary Email Address","Attachments":"Attachments","Blocked":"Blocked","Emails from":"Emails from","are blocked by Admin":"are blocked by Admin","No Messages":"No Messages","Waiting for Incoming Messages":"Waiting for Incoming Messages","Scan QR Code to access":"Scan QR Code to access","Create your own Temp Mail":"Create your own Temp Mail","Your Temprorary Email":"Your Temprorary Email","Enter a Username and Select the Domain":"Enter a Username and Select the Domain","Username length cannot be less than":"Username length cannot be less than","and greator than":"and greator than","Create a Random Email":"Create a Random Email","Sender":"Sender","Subject":"Subject","Time":"Time","Open":"Open","Go Back to Inbox":"Go Back to Inbox","Date":"Date","Copyright":"Copyright","Ad Blocker Detected":"Ad Blocker Detected","Disable the Ad Blocker to use ":"Disable the Ad Blocker to use ","Your temporary email address is ready":"Your temporary email address is ready","You have reached daily limit of MAX ":"You have reached daily limit of MAX "," temp mail":" temp mail","Sorry! That email is already been used by someone else. Please try a different email address.":"Sorry! That email is already been used by someone else. Please try a different email address.","Invalid Captcha. Please try again":"Invalid Captcha. Please try again","Invalid Password":"Invalid Password","Password":"Password","Unlock":"Unlock","Your Name":"Your Name","Enter your Name":"Enter your Name","Your Email":"Your Email","Enter your Email":"Enter your Email","Message":"Message","Enter your Message":"Enter your Message","Send Message":"Send Message"}
  const __ = (string) => {
    if(strings[string] !== undefined) {
      return strings[string];
    } else {
      return string;
    }
  }
</script>
</head>
<body>
    <div class="default-theme">
        <div class="flex flex-wrap">
            <div class="w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen" style="background-color: #0155b5">
                <div class="flex justify-center p-3 mb-10">
                    <a href="https://dujaw.com">
                                                <img class="w-logo" src="https://dujaw.com/storage/public/images/joystick.png" alt="logo">
                                            </a>
                </div>
                                <div wire:id="G1eQkNJBuO6zCruB7LD6" wire:initial-data="{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;G1eQkNJBuO6zCruB7LD6&quot;,&quot;name&quot;:&quot;frontend.actions&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;syncEmail&quot;,&quot;checkReCaptcha3&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;f397053a&quot;,&quot;data&quot;:{&quot;in_app&quot;:false,&quot;user&quot;:null,&quot;domain&quot;:null,&quot;domains&quot;:[&quot;dujaw.com&quot;,&quot;fgeta.com&quot;,&quot;dennisgls26.com&quot;,&quot;aipicz.com&quot;,&quot;gamersparky26.com&quot;,&quot;withsd.com&quot;,&quot;zzetu.com&quot;,&quot;dxgamers.com&quot;,&quot;ulnik.com&quot;,&quot;rdmail.info&quot;,&quot;ziuwi.com&quot;,&quot;tseru.com&quot;,&quot;gohuki.com&quot;,&quot;1em0nstore.win&quot;,&quot;1em0nstore.trade&quot;,&quot;1emonstore.trade&quot;],&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;emails&quot;:[&quot;<EMAIL>&quot;],&quot;captcha&quot;:null},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;9bc158788884c3cddce7441d956c7a8fbb55dd8ea909993642afceb221fb6a4d&quot;}}" x-data="{ in_app: false }">
    <div x-show.transition.in="in_app" class="app-action mt-4 px-8" style="display: none;">
                <form wire:submit.prevent="create" class="lg:max-w-72 lg:mx-auto" method="post">
                        <input class="block appearance-none w-full border-0 rounded-md py-4 px-5 bg-white text-white bg-opacity-10 focus:outline-none placeholder-white placeholder-opacity-50" type="text" name="user" id="user" wire:model.defer="user" placeholder="Enter Username">
            <div class="divider mt-5"></div>
            <div class="relative">
                <div class="relative" x-data="{ open: false }" @click.away="open = false" @close.stop="open = false">
    <div @click="open = ! open">
        <input x-ref="domain" type="text" class="block appearance-none w-full border-0 bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none placeholder-white placeholder-opacity-50" placeholder="Select Domain" name="domain" id="domain" wire:model="domain" readonly>
    </div>

    <div x-show="open"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top-right right-0"
            style="display: none;"
            @click="open = false">
        <div class="rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white">
            <a x-on:click="$refs.domain.value = 'dujaw.com'; $wire.setDomain('dujaw.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dujaw.com</a>
                                                <a x-on:click="$refs.domain.value = 'fgeta.com'; $wire.setDomain('fgeta.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>fgeta.com</a>
                                                <a x-on:click="$refs.domain.value = 'dennisgls26.com'; $wire.setDomain('dennisgls26.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dennisgls26.com</a>
                                                <a x-on:click="$refs.domain.value = 'aipicz.com'; $wire.setDomain('aipicz.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>aipicz.com</a>
                                                <a x-on:click="$refs.domain.value = 'gamersparky26.com'; $wire.setDomain('gamersparky26.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gamersparky26.com</a>
                                                <a x-on:click="$refs.domain.value = 'withsd.com'; $wire.setDomain('withsd.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>withsd.com</a>
                                                <a x-on:click="$refs.domain.value = 'zzetu.com'; $wire.setDomain('zzetu.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>zzetu.com</a>
                                                <a x-on:click="$refs.domain.value = 'dxgamers.com'; $wire.setDomain('dxgamers.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dxgamers.com</a>
                                                <a x-on:click="$refs.domain.value = 'ulnik.com'; $wire.setDomain('ulnik.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ulnik.com</a>
                                                <a x-on:click="$refs.domain.value = 'rdmail.info'; $wire.setDomain('rdmail.info')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>rdmail.info</a>
                                                <a x-on:click="$refs.domain.value = 'ziuwi.com'; $wire.setDomain('ziuwi.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ziuwi.com</a>
                                                <a x-on:click="$refs.domain.value = 'tseru.com'; $wire.setDomain('tseru.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>tseru.com</a>
                                                <a x-on:click="$refs.domain.value = 'gohuki.com'; $wire.setDomain('gohuki.com')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gohuki.com</a>
                                                <a x-on:click="$refs.domain.value = '1em0nstore.win'; $wire.setDomain('1em0nstore.win')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.win</a>
                                                <a x-on:click="$refs.domain.value = '1em0nstore.trade'; $wire.setDomain('1em0nstore.trade')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.trade</a>
                                                <a x-on:click="$refs.domain.value = '1emonstore.trade'; $wire.setDomain('1emonstore.trade')" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1emonstore.trade</a>
        </div>
    </div>
</div>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-5 text-white">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                </div>
            </div>
            <div class="divider mt-5"></div>
            <input id="create" class="block appearance-none w-full rounded-md py-4 px-5 bg-teal-500 text-white cursor-pointer focus:outline-none" style="background-color: #2fc10a" type="submit" value="Create">
            <div class="divider my-8 flex justify-center">
                <div class="border-t-2 w-2/3 border-white border-opacity-25"></div>
            </div>
        </form>
        <form wire:submit.prevent="random" class="lg:max-w-72 lg:mx-auto" method="post">
            <input id="random" class="block appearance-none w-full rounded-md py-4 px-5 bg-yellow-500 text-white cursor-pointer focus:outline-none" style="background-color: #d2ab3e" type="submit" value="Random">
        </form>
                <div class="lg:max-w-72 lg:mx-auto">
            <button x-on:click="in_app = false" class="block appearance-none w-full rounded-md my-5 py-2 px-5 bg-white bg-opacity-10 text-white text-sm cursor-pointer focus:outline-none">Cancel</button>
        </div>
            </div>
    <div x-show.transition.in="!in_app" class="in-app-actions mt-4 px-8" style="display: none;">
        <form class="lg:max-w-72 lg:mx-auto" action="#" method="post">
            <div class="relative">
                <div class="relative" x-data="{ open: false }" @click.away="open = false" @close.stop="open = false">
    <div @click="open = ! open">
        <div class="block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none" id="email_id"><EMAIL></div>
    </div>

    <div x-show="open"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top"
            style="display: none;"
            @click="open = false">
        <div class="rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white">
            <a class="block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out" href="https://dujaw.com/switch/<EMAIL>"><EMAIL></a>
        </div>
    </div>
</div>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                </div>
            </div>
        </form>
        <div class="divider mt-5"></div>
        <div class="grid grid-cols-4 lg:grid-cols-2 gap-2 lg:gap-6 lg:max-w-72 lg:mx-auto">
            <div class="btn_copy bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer">
                <div class="text-xl lg:text-3xl mx-auto">
                    <i class="far fa-copy"></i>
                </div>
                <div class="text-xs lg:text-base pt-5">Copy</div>
            </div>
            <div onclick="document.getElementById('refresh').classList.remove('pause-spinner')" wire:click="$emit('fetchMessages')" class="bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer">
                <div class="text-xl lg:text-3xl  mx-auto">
                    <i id="refresh" class="fas fa-sync-alt fa-spin"></i>
                </div>
                <div class="text-xs lg:text-base pt-5">Refresh</div>
            </div>
            <div x-on:click="in_app = true" class="bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer">
                <div class="text-xl lg:text-3xl  mx-auto">
                    <i class="far fa-plus-square"></i>
                </div>
                <div class="text-xs lg:text-base pt-5">New</div>
            </div>
            <div wire:click="deleteEmail" class="bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer">
                <div class="text-xl lg:text-3xl  mx-auto">
                    <i class="far fa-trash-alt"></i>
                </div>
                <div class="text-xs lg:text-base pt-5">Delete</div>
            </div>
        </div>
    </div>
    </div>
<!-- Livewire Component wire-end:G1eQkNJBuO6zCruB7LD6 -->                            </div>
            <div class="w-full lg:w-3/4">
                <nav wire:id="1vPaXhnkUDrlTlnUnhNH" wire:initial-data="{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;1vPaXhnkUDrlTlnUnhNH&quot;,&quot;name&quot;:&quot;frontend.nav&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;245a7233&quot;,&quot;data&quot;:{&quot;menus&quot;:[],&quot;current_route&quot;:null},&quot;dataMeta&quot;:{&quot;modelCollections&quot;:{&quot;menus&quot;:{&quot;class&quot;:null,&quot;id&quot;:[],&quot;relations&quot;:[],&quot;connection&quot;:null}}},&quot;checksum&quot;:&quot;7ca89e39ca3297e3edc4f5485b481d540b0add19fe69aa7b90fab80732c00ea6&quot;}}">
    <div class="bg-gray-100 px-5 hidden lg:flex sticky top-0 z-40 h-24">
        <div class="w-full my-auto">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="flex items-baseline space-x-4">
                                                                    </div>
                </div>
                <div class="flex items-center">
                    <div>
                                            </div>
                    <div class="ml-4 flex items-center md:ml-6">
                        <div class="relative">
                            <form action="https://dujaw.com/locale" id="locale-form" method="post">
                                <input type="hidden" name="_token" value="nUSJbGWH46tyUOVWwPxj40E0Wy4AcLdb3IzjYBaA">                                <select class="block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none" name="locale" id="locale">
                                                                        <option >ar</option>
                                                                        <option >de</option>
                                                                        <option selected>en</option>
                                                                        <option >fr</option>
                                                                        <option >hi</option>
                                                                        <option >pl</option>
                                                                        <option >ru</option>
                                                                        <option >es</option>
                                                                        <option >vi</option>
                                                                        <option >tr</option>
                                                                        <option >no</option>
                                                                        <option >id</option>
                                                                        <option >it</option>
                                                                    </select>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div x-data="{ open: false }">
        <div @click="open = true" class="absolute top-12 right-6 w-8 text-white">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
            </svg>
        </div>
        <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" @click.away="open = false" class="flex-col lg:hidden fixed top-0 left-0 min-h-screen w-full bg-black bg-opacity-75">
            <div @click="open = false" class="absolute top-6 right-6 w-8 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </div>
            <div class="w-full mx-auto mt-20">
                <div class="flex flex-col items-center justify-between">
                    <div class="flex flex-col items-center space-y-2">
                                                                    </div>
                    <div class="flex flex-col items-center space-y-2 mt-10">
                        <div class="text-white space-x-2">
                                                    </div>
                        <div class="flex items-center mt-4">
                            <div class="relative">
                                <form action="https://dujaw.com/locale" id="locale-form-mobile" method="post">
                                    <input type="hidden" name="_token" value="nUSJbGWH46tyUOVWwPxj40E0Wy4AcLdb3IzjYBaA">                                    <select class="block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none" name="locale" id="locale-mobile">
                                                                                <option >ar</option>
                                                                                <option >de</option>
                                                                                <option selected>en</option>
                                                                                <option >fr</option>
                                                                                <option >hi</option>
                                                                                <option >pl</option>
                                                                                <option >ru</option>
                                                                                <option >es</option>
                                                                                <option >vi</option>
                                                                                <option >tr</option>
                                                                                <option >no</option>
                                                                                <option >id</option>
                                                                                <option >it</option>
                                                                            </select>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>
<!-- Livewire Component wire-end:1vPaXhnkUDrlTlnUnhNH -->                <div class="flex flex-col lg:min-h-tm-default">
                                         
                        <main wire:id="KUF4U9vGveWnUOFFDjnR" wire:initial-data="{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;KUF4U9vGveWnUOFFDjnR&quot;,&quot;name&quot;:&quot;frontend.app&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;fetchMessages&quot;,&quot;syncEmail&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;36d69e5b&quot;,&quot;data&quot;:{&quot;messages&quot;:[],&quot;deleted&quot;:[],&quot;error&quot;:&quot;&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;initial&quot;:false,&quot;overflow&quot;:false},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;c1327a7d5cea4c090545550bca836b4b713e6413f143aa6f1dbe92b93bf5b926&quot;}}" x-data="{ id: 0 }" class="flex-1 lg:flex">
            <div class="w-full lg:w-1/3 bg-white flex flex-col min-h-tm-mobile">
                <div class="flex-1 flex justify-center items-center h-40 text-gray-400 text-2xl">
            Fetching...
        </div>
            </div>
    <div class="message-content w-full lg:w-2/3 bg-white border-1 border-l border-gray-200 flex flex-col">
        <div x-show="id === 0" class="flex-1 hidden lg:flex">
            <div class="w-2/3 m-auto">
                <img class="m-auto max-w-full" src="https://dujaw.com/images/sample.jpg" alt="mails">
            </div>
        </div>
            </div>
</main>
<!-- Livewire Component wire-end:KUF4U9vGveWnUOFFDjnR -->                                                        </div>
            </div>
        </div>
    </div>
    
    <!--- Helper Text for Language Translation -->
    <div class="hidden language-helper">
        <div class="error">Error</div>
        <div class="success">Success</div>
        <div class="copy_text">Email ID Copied to Clipboard</div>
    </div>

    <script src="/livewire/livewire.js?id=90730a3b0e7144480175" data-turbo-eval="false" data-turbolinks-eval="false" ></script><script data-turbo-eval="false" data-turbolinks-eval="false" >window.livewire = new Livewire();window.Livewire = window.livewire;window.livewire_app_url = '';window.livewire_token = 'nUSJbGWH46tyUOVWwPxj40E0Wy4AcLdb3IzjYBaA';window.deferLoadingAlpine = function (callback) {window.addEventListener('livewire:load', function () {callback();});};let started = false;window.addEventListener('alpine:initializing', function () {if (! started) {window.livewire.start();started = true;}});document.addEventListener("DOMContentLoaded", function () {if (! started) {window.livewire.start();started = true;}});</script>
        <script>
        document.addEventListener('DOMContentLoaded', () => {
            const email = '<EMAIL>';
            const add_mail_in_title = "yes"
            if(add_mail_in_title === 'yes') {
                document.title += ` - ${email}`;
            }
            Livewire.emit('syncEmail', email);
            Livewire.emit('fetchMessages');
        });
    </script>
        <script>
        document.addEventListener('stopLoader', () => {
            document.getElementById('refresh').classList.add('pause-spinner');
        });
        let counter = parseInt(20);
        setInterval(() => {
            if (counter === 0 && document.getElementById('imap-error') === null && !document.hidden) {
                document.getElementById('refresh').classList.remove('pause-spinner');
                Livewire.emit('fetchMessages');
                counter = parseInt(20);
            }
            counter--;
            if(document.hidden) {
                counter = 1;
            }
        }, 1000);
    </script>
    
    
        <script src="https://dujaw.com/storage/js/mnpw3.js" defer></script>
    <script defer>
    setTimeout(() => {
        const enable_ad_block_detector = "0"
        if(!document.getElementById('Q8CvrZzY9fphm6gG') && enable_ad_block_detector == "1") {
            document.querySelector('.default-theme').remove()
            document.querySelector('body > div').insertAdjacentHTML('beforebegin', `
                <div class="fixed w-screen h-screen bg-red-800 flex flex-col justify-center items-center gap-5 z-50 text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-40 w-40" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                    </svg>
                    <h1 class="text-4xl font-bold">Ad Blocker Detected</h1>
                    <h2>Disable the Ad Blocker to use Dujaw Store</h2>
                </div>
            `)
        }
    }, 500);
    </script>
    </body>
</html>