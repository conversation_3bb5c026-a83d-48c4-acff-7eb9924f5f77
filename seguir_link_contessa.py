#!/usr/bin/env python3
"""
Script para seguir el link de aprobación de Contessa
"""

import requests
from bs4 import BeautifulSoup
from datetime import datetime


def seguir_link_contessa():
    """Sigue el link de aprobación de Contessa"""
    link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/dcfe346f2983da54fd931425552a3b8e"
    
    print("🔗 SIGUIENDO LINK DE CONTESSA")
    print("=" * 60)
    print(f"📧 Email: <EMAIL>")
    print(f"🔗 Link: {link}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        print("🌐 Accediendo al link...")
        response = session.get(link, timeout=30)
        
        print(f"📥 Respuesta: {response.status_code}")
        print(f"📄 Tipo: {response.headers.get('content-type', 'N/A')}")
        print(f"📏 Tamaño: {len(response.text)} caracteres")
        
        if response.status_code == 200:
            print("✅ Link accesible")
            
            # Guardar HTML
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"contessa_approval_page_{timestamp}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 HTML guardado: {filename}")
            
            # Analizar contenido
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Título
            title = soup.find('title')
            if title:
                print(f"\n📋 Título: {title.get_text(strip=True)}")
            
            # Encabezados
            print(f"\n📌 ENCABEZADOS:")
            for i in range(1, 4):
                headings = soup.find_all(f'h{i}')
                for heading in headings:
                    text = heading.get_text(strip=True)
                    if text:
                        print(f"   H{i}: {text}")
            
            # Buscar formularios
            forms = soup.find_all('form')
            print(f"\n📝 FORMULARIOS: {len(forms)}")
            
            for i, form in enumerate(forms, 1):
                action = form.get('action', '')
                method = form.get('method', 'GET')
                print(f"\n  📋 Formulario {i}:")
                print(f"     Acción: {action}")
                print(f"     Método: {method}")
                
                # Inputs
                inputs = form.find_all(['input', 'select', 'textarea', 'button'])
                for input_elem in inputs:
                    input_type = input_elem.get('type', '')
                    name = input_elem.get('name', '')
                    value = input_elem.get('value', '')
                    text = input_elem.get_text(strip=True)
                    
                    if input_type in ['submit', 'button'] or input_elem.name == 'button':
                        print(f"     🔘 Botón: {text or value}")
                    elif input_type == 'hidden':
                        print(f"     🔒 Campo oculto: {name} = {value}")
                    else:
                        print(f"     📝 Campo: {name} ({input_type})")
            
            # Buscar botones independientes
            buttons = soup.find_all('button')
            independent_buttons = [btn for btn in buttons if not btn.find_parent('form')]
            
            if independent_buttons:
                print(f"\n🔘 BOTONES INDEPENDIENTES: {len(independent_buttons)}")
                for button in independent_buttons:
                    text = button.get_text(strip=True)
                    onclick = button.get('onclick', '')
                    print(f"  🔘 {text}")
                    if onclick:
                        print(f"     onClick: {onclick}")
            
            # Buscar links importantes
            links = soup.find_all('a', href=True)
            important_links = []
            
            for link_elem in links:
                href = link_elem.get('href')
                text = link_elem.get_text(strip=True)
                
                if any(keyword in href.lower() for keyword in ['approve', 'reject', 'confirm', 'cancel']):
                    important_links.append((text, href))
            
            if important_links:
                print(f"\n🔗 LINKS IMPORTANTES: {len(important_links)}")
                for text, href in important_links:
                    print(f"  🔗 {text}: {href}")
            
            # Buscar mensajes de estado
            text_content = soup.get_text().lower()
            
            print(f"\n📊 ANÁLISIS DEL CONTENIDO:")
            
            if 'invalid' in text_content or 'expired' in text_content or 'timed out' in text_content:
                print("❌ ESTADO: Link expirado o inválido")
                return False
            elif 'approve' in text_content and 'email' in text_content:
                print("✅ ESTADO: Página de aprobación activa")
                
                # Buscar si hay formularios para completar
                if forms:
                    print("🎯 ACCIÓN POSIBLE: Hay formularios disponibles para completar")
                    
                    # Intentar completar automáticamente
                    return intentar_completar_aprobacion(session, forms, response.url)
                else:
                    print("⚠️ ACCIÓN REQUERIDA: No hay formularios, posiblemente requiere JavaScript")
                    return True
            else:
                print("❓ ESTADO: Contenido incierto")
                return None
                
        elif response.status_code == 404:
            print("❌ Link no encontrado (404) - Posiblemente expirado")
            return False
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def intentar_completar_aprobacion(session, forms, base_url):
    """Intenta completar automáticamente la aprobación"""
    print(f"\n🤖 INTENTANDO COMPLETAR APROBACIÓN AUTOMÁTICAMENTE")
    print("-" * 50)
    
    for i, form in enumerate(forms, 1):
        print(f"\n📋 Procesando formulario {i}...")
        
        action = form.get('action', '')
        method = form.get('method', 'POST').upper()
        
        # Construir URL completa
        if action.startswith('http'):
            form_url = action
        elif action.startswith('/'):
            form_url = f"https://club.pokemon.com{action}"
        else:
            form_url = f"{base_url.rsplit('/', 1)[0]}/{action}" if action else base_url
            
        print(f"🔗 URL del formulario: {form_url}")
        print(f"📤 Método: {method}")
        
        # Recopilar datos del formulario
        form_data = {}
        
        inputs = form.find_all(['input', 'select', 'textarea'])
        for input_elem in inputs:
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', '')
            
            if name:
                if input_type == 'checkbox':
                    # Solo incluir checkboxes marcados
                    if input_elem.has_attr('checked'):
                        form_data[name] = value or 'on'
                elif input_type == 'radio':
                    # Solo incluir radios marcados
                    if input_elem.has_attr('checked'):
                        form_data[name] = value
                elif input_type not in ['submit', 'button', 'reset']:
                    form_data[name] = value
        
        print(f"📝 Datos del formulario:")
        for key, value in form_data.items():
            if 'token' in key.lower() or 'csrf' in key.lower():
                print(f"   🔒 {key}: {value[:20]}..." if len(value) > 20 else f"   🔒 {key}: {value}")
            else:
                print(f"   📝 {key}: {value}")
        
        # Buscar botón de aprobación
        approve_buttons = form.find_all(['input', 'button'], 
                                       attrs={'type': ['submit', 'button']})
        
        approve_button = None
        for button in approve_buttons:
            button_text = (button.get('value', '') + ' ' + button.get_text(strip=True)).lower()
            if any(word in button_text for word in ['approve', 'confirm', 'accept', 'yes']):
                approve_button = button
                break
        
        if approve_button:
            button_name = approve_button.get('name')
            button_value = approve_button.get('value', '')
            
            if button_name:
                form_data[button_name] = button_value
                
            print(f"🎯 Botón de aprobación encontrado: {button_value or approve_button.get_text(strip=True)}")
            
            try:
                print(f"📤 Enviando formulario...")
                
                if method == 'POST':
                    response = session.post(form_url, data=form_data, timeout=30)
                else:
                    response = session.get(form_url, params=form_data, timeout=30)
                
                print(f"📥 Respuesta: {response.status_code}")
                
                if response.status_code == 200:
                    # Analizar respuesta
                    soup = BeautifulSoup(response.text, 'html.parser')
                    text_content = soup.get_text().lower()
                    
                    if 'success' in text_content or 'approved' in text_content or 'confirmed' in text_content:
                        print("🎉 ¡APROBACIÓN EXITOSA!")
                        
                        # Guardar página de éxito
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"contessa_success_{timestamp}.html"
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        print(f"💾 Página de éxito guardada: {filename}")
                        
                        return True
                    elif 'error' in text_content or 'invalid' in text_content:
                        print("❌ Error en la aprobación")
                        return False
                    else:
                        print("❓ Resultado incierto")
                        
                        # Guardar para análisis
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"contessa_result_{timestamp}.html"
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        print(f"💾 Resultado guardado: {filename}")
                        
                        return None
                else:
                    print(f"❌ Error en envío: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Error enviando formulario: {e}")
                return False
        else:
            print("⚠️ No se encontró botón de aprobación en este formulario")
    
    print("❌ No se pudo completar la aprobación automáticamente")
    return False


def main():
    """Función principal"""
    print("🚀 SEGUIDOR DE LINK DE CONTESSA")
    print("=" * 50)
    
    resultado = seguir_link_contessa()
    
    print(f"\n📊 RESULTADO FINAL:")
    if resultado is True:
        print("✅ Link activo y procesado exitosamente")
    elif resultado is False:
        print("❌ Link inválido, expirado o error en procesamiento")
    else:
        print("❓ Estado incierto - requiere revisión manual")
    
    print(f"\n🏁 Proceso completado")


if __name__ == "__main__":
    main()
