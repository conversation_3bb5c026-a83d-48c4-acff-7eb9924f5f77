#!/usr/bin/env python3
"""
Script de automatización web para capturar flujo de gestión de dominios/correos
Versión simplificada que funciona con requests y selenium como fallback
"""

import json
import time
import os
import sys
from datetime import datetime
from typing import List, Dict, Any

# Intentar importar playwright, si no está disponible usar selenium
try:
    import asyncio
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
    print("✅ Playwright disponible")
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright no disponible, usando modo básico")

# Intentar importar selenium como fallback
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
    print("✅ Selenium disponible como fallback")
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️ Selenium no disponible")

import requests


class WebFlowCapture:
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.network_requests = []
        self.network_responses = []
        self.screenshots_dir = "screenshots"
        self.logs_dir = "logs"
        
    async def setup_browser(self):
        """Configura el navegador y la página"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        self.page = await self.context.new_page()
        
        # Configurar interceptación de red
        await self.setup_network_interception()
        
    async def setup_network_interception(self):
        """Configura la interceptación de llamadas de red"""
        
        async def handle_request(request):
            """Captura todas las peticiones HTTP"""
            request_data = {
                'timestamp': datetime.now().isoformat(),
                'method': request.method,
                'url': request.url,
                'headers': dict(request.headers),
                'post_data': request.post_data if request.post_data else None,
                'resource_type': request.resource_type
            }
            self.network_requests.append(request_data)
            print(f"📤 REQUEST: {request.method} {request.url}")
            
        async def handle_response(response):
            """Captura todas las respuestas HTTP"""
            try:
                # Solo capturar el contenido de respuestas relevantes (no imágenes, etc.)
                content = None
                if response.request.resource_type in ['xhr', 'fetch', 'document']:
                    try:
                        content = await response.text()
                    except:
                        content = "Error reading response content"
                
                response_data = {
                    'timestamp': datetime.now().isoformat(),
                    'status': response.status,
                    'url': response.url,
                    'headers': dict(response.headers),
                    'content': content,
                    'resource_type': response.request.resource_type
                }
                self.network_responses.append(response_data)
                print(f"📥 RESPONSE: {response.status} {response.url}")
            except Exception as e:
                print(f"Error capturing response: {e}")
        
        self.page.on('request', handle_request)
        self.page.on('response', handle_response)
        
    async def navigate_to_site(self, url: str):
        """Navega al sitio web objetivo"""
        print(f"🌐 Navegando a: {url}")
        await self.page.goto(url, wait_until='networkidle')
        await self.take_screenshot("01_initial_page")
        
    async def login(self, username: str = None, password: str = None):
        """Realiza el proceso de login"""
        print("🔐 Iniciando proceso de login...")
        
        # Buscar campos de login comunes
        username_selectors = [
            'input[type="email"]',
            'input[type="text"][name*="user"]',
            'input[type="text"][name*="email"]',
            'input[name="username"]',
            'input[name="email"]',
            'input[id*="user"]',
            'input[id*="email"]'
        ]
        
        password_selectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[id*="password"]'
        ]
        
        # Intentar encontrar campos de usuario
        username_field = None
        for selector in username_selectors:
            try:
                username_field = await self.page.wait_for_selector(selector, timeout=2000)
                if username_field:
                    print(f"✅ Campo de usuario encontrado: {selector}")
                    break
            except:
                continue
                
        # Intentar encontrar campo de contraseña
        password_field = None
        for selector in password_selectors:
            try:
                password_field = await self.page.wait_for_selector(selector, timeout=2000)
                if password_field:
                    print(f"✅ Campo de contraseña encontrado: {selector}")
                    break
            except:
                continue
        
        if username_field and password_field:
            if username:
                await username_field.fill(username)
                print("📝 Usuario ingresado")
            if password:
                await password_field.fill(password)
                print("📝 Contraseña ingresada")
                
            await self.take_screenshot("02_login_filled")
            
            # Buscar botón de submit
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Login")',
                'button:has-text("Entrar")',
                'button:has-text("Iniciar")',
                '.login-button',
                '#login-button'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_btn = await self.page.wait_for_selector(selector, timeout=2000)
                    if submit_btn:
                        print(f"🔘 Botón de login encontrado: {selector}")
                        await submit_btn.click()
                        await self.page.wait_for_load_state('networkidle')
                        await self.take_screenshot("03_after_login")
                        return True
                except:
                    continue
        
        print("❌ No se pudieron encontrar los campos de login")
        return False
        
    async def wait_for_manual_interaction(self, message: str = "Presiona Enter cuando hayas completado la acción manual..."):
        """Pausa para permitir interacción manual"""
        print(f"⏸️  {message}")
        input()  # Esperar input del usuario
        await self.take_screenshot(f"manual_action_{int(time.time())}")
        
    async def take_screenshot(self, name: str):
        """Toma una captura de pantalla"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screenshot_{timestamp}_{name}.png"
        await self.page.screenshot(path=filename, full_page=True)
        print(f"📸 Captura guardada: {filename}")
        
    async def save_network_data(self):
        """Guarda los datos de red capturados"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Guardar requests
        with open(f"network_requests_{timestamp}.json", 'w', encoding='utf-8') as f:
            json.dump(self.network_requests, f, indent=2, ensure_ascii=False)
            
        # Guardar responses
        with open(f"network_responses_{timestamp}.json", 'w', encoding='utf-8') as f:
            json.dump(self.network_responses, f, indent=2, ensure_ascii=False)
            
        print(f"💾 Datos de red guardados: {len(self.network_requests)} requests, {len(self.network_responses)} responses")
        
    async def analyze_api_calls(self):
        """Analiza las llamadas de API capturadas"""
        print("\n🔍 ANÁLISIS DE LLAMADAS DE API:")
        print("=" * 50)
        
        api_calls = []
        for req in self.network_requests:
            if req['resource_type'] in ['xhr', 'fetch'] or '/api/' in req['url']:
                api_calls.append(req)
                
        for call in api_calls:
            print(f"🔗 {call['method']} {call['url']}")
            if call['post_data']:
                print(f"   📤 Data: {call['post_data'][:200]}...")
            print()
            
        return api_calls
        
    async def cleanup(self):
        """Limpia recursos"""
        await self.browser.close()
        await self.playwright.stop()


async def main():
    """Función principal"""
    print("🚀 Iniciando captura de flujo web...")
    
    # Solicitar URL del sitio
    site_url = input("🌐 Ingresa la URL del sitio web: ").strip()
    if not site_url.startswith(('http://', 'https://')):
        site_url = 'https://' + site_url
    
    # Crear instancia del capturador
    capture = WebFlowCapture(headless=False)  # Modo visual para interacción
    
    try:
        await capture.setup_browser()
        await capture.navigate_to_site(site_url)
        
        # Intentar login automático o manual
        print("\n¿Quieres intentar login automático? (y/n)")
        auto_login = input().lower().strip() == 'y'
        
        if auto_login:
            username = input("👤 Usuario/Email: ").strip()
            password = input("🔒 Contraseña: ").strip()
            login_success = await capture.login(username, password)
            
            if not login_success:
                await capture.wait_for_manual_interaction("Login automático falló. Completa el login manualmente y presiona Enter...")
        else:
            await capture.wait_for_manual_interaction("Completa el login manualmente y presiona Enter...")
        
        # Permitir navegación manual para capturar el flujo
        await capture.wait_for_manual_interaction("Navega por el sitio y realiza las acciones que quieres automatizar. Presiona Enter cuando termines...")
        
        # Analizar y guardar datos
        await capture.analyze_api_calls()
        await capture.save_network_data()
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await capture.cleanup()
        
    print("✅ Captura completada. Revisa los archivos generados para el análisis de API.")


if __name__ == "__main__":
    asyncio.run(main())
