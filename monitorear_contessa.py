#!/usr/bin/env python3
"""
Script para monitorear continuamente el mailbox de Contessa hasta que aparezcan correos
"""

import time
import json
from datetime import datetime
from nuevo_mailbox_contessa import ContessaMailboxReader


class MonitorContessa:
    def __init__(self):
        self.reader = ContessaMailboxReader()
        self.correos_encontrados = []
        
    def verificar_correos(self):
        """Verifica si hay correos nuevos"""
        try:
            print(f"🔍 {datetime.now().strftime('%H:%M:%S')} - Verificando correos...")
            
            # Hacer unlock
            if not self.reader.unlock_mailbox():
                print("❌ Error en unlock")
                return False
                
            # Extraer datos Livewire
            livewire_data = self.reader.extraer_datos_livewire_del_html()
            if not livewire_data:
                print("❌ No se pudieron extraer datos de Livewire")
                return False
                
            # Verificar mensajes iniciales
            initial_messages = livewire_data['initial_data']['serverMemo']['data']['messages']
            if len(initial_messages) > 0:
                print(f"🎉 ¡{len(initial_messages)} mensajes encontrados en datos iniciales!")
                self.procesar_mensajes_encontrados(initial_messages)
                return True
                
            # Hacer llamadas Livewire
            resultados = self.reader.hacer_llamadas_livewire(livewire_data)
            
            # Verificar si hay mensajes en las respuestas
            total_mensajes = 0
            for tipo, data in resultados:
                if isinstance(data, dict) and 'serverMemo' in data and 'data' in data['serverMemo']:
                    server_data = data['serverMemo']['data']
                    if 'messages' in server_data and len(server_data['messages']) > 0:
                        total_mensajes += len(server_data['messages'])
                        print(f"🎉 ¡{len(server_data['messages'])} mensajes encontrados en {tipo}!")
                        self.procesar_mensajes_encontrados(server_data['messages'])
                        return True
                        
            print(f"📭 Sin mensajes aún (total: {total_mensajes})")
            return False
            
        except Exception as e:
            print(f"❌ Error verificando correos: {e}")
            return False
        finally:
            self.reader.logout()
            
    def procesar_mensajes_encontrados(self, mensajes):
        """Procesa los mensajes encontrados"""
        print(f"\n📧 PROCESANDO {len(mensajes)} MENSAJES")
        print("=" * 50)
        
        links_aprobacion = []
        
        for i, mensaje in enumerate(mensajes, 1):
            print(f"\n📧 Mensaje {i}:")
            
            if isinstance(mensaje, dict):
                # Mostrar información del mensaje
                if 'subject' in mensaje:
                    print(f"   📋 Asunto: {mensaje['subject']}")
                if 'from' in mensaje:
                    print(f"   👤 De: {mensaje['from']}")
                if 'date' in mensaje:
                    print(f"   📅 Fecha: {mensaje['date']}")
                if 'body' in mensaje:
                    body = mensaje['body']
                    print(f"   📄 Contenido: {body[:100]}...")
                    
                    # Buscar links de aprobación
                    import re
                    pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
                    links = re.findall(pattern, body, re.IGNORECASE)
                    
                    for link in links:
                        links_aprobacion.append({
                            'mensaje_num': i,
                            'link': link,
                            'mensaje': mensaje
                        })
                        print(f"   🔗 Link encontrado: {link}")
            else:
                print(f"   📄 Contenido: {str(mensaje)[:100]}...")
                
        self.correos_encontrados = mensajes
        
        if links_aprobacion:
            print(f"\n🎯 ¡{len(links_aprobacion)} LINKS DE APROBACIÓN ENCONTRADOS!")
            self.seguir_link_mas_reciente(links_aprobacion)
        else:
            print(f"\n⚠️ No se encontraron links de aprobación en los mensajes")
            
    def seguir_link_mas_reciente(self, links_aprobacion):
        """Sigue el link de aprobación más reciente"""
        if not links_aprobacion:
            return
            
        # Tomar el primer link (más reciente)
        link_data = links_aprobacion[0]
        link = link_data['link']
        
        print(f"\n🔗 SIGUIENDO LINK MÁS RECIENTE:")
        print(f"   {link}")
        
        try:
            from seguir_link_aprobacion import PokemonLinkFollower
            follower = PokemonLinkFollower()
            resultado = follower.seguir_link(link)
            
            if resultado:
                print(f"\n✅ Link seguido exitosamente")
                
                # Guardar resultado
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"contessa_link_resultado_{timestamp}.json"
                
                data_completa = {
                    'timestamp': datetime.now().isoformat(),
                    'email': '<EMAIL>',
                    'link_seguido': link,
                    'mensajes': self.correos_encontrados,
                    'links_aprobacion': links_aprobacion,
                    'resultado_link': resultado
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data_completa, f, indent=2, ensure_ascii=False)
                    
                print(f"💾 Resultado completo guardado en: {filename}")
            else:
                print(f"\n❌ Error siguiendo el link")
                
        except Exception as e:
            print(f"❌ Error siguiendo link: {e}")
            
    def monitorear_continuo(self, intervalo_segundos=30, max_intentos=20):
        """Monitorea continuamente hasta encontrar correos"""
        print("🔄 MONITOREANDO MAILBOX DE CONTESSA")
        print("=" * 60)
        print(f"📧 Email: <EMAIL>")
        print(f"⏱️ Intervalo: {intervalo_segundos} segundos")
        print(f"🔢 Máximo intentos: {max_intentos}")
        print("=" * 60)
        print("💡 Presiona Ctrl+C para detener")
        
        for intento in range(max_intentos):
            print(f"\n🔍 INTENTO {intento + 1}/{max_intentos} - {datetime.now().strftime('%H:%M:%S')}")
            print("-" * 40)
            
            if self.verificar_correos():
                print(f"\n🎉 ¡CORREOS ENCONTRADOS!")
                print(f"🏁 Monitoreo completado exitosamente")
                return True
                
            if intento < max_intentos - 1:
                print(f"⏳ Esperando {intervalo_segundos} segundos antes del siguiente intento...")
                try:
                    time.sleep(intervalo_segundos)
                except KeyboardInterrupt:
                    print(f"\n⏹️ Monitoreo detenido por el usuario")
                    return False
                    
        print(f"\n😞 No se encontraron correos después de {max_intentos} intentos")
        print(f"💡 Puedes ejecutar el script de nuevo más tarde")
        return False
        
    def verificacion_rapida(self):
        """Hace una verificación rápida sin monitoreo continuo"""
        print("⚡ VERIFICACIÓN RÁPIDA")
        print("=" * 30)
        
        return self.verificar_correos()


def main():
    """Función principal"""
    print("🚀 MONITOR DE MAILBOX CONTESSA")
    print("=" * 50)
    
    monitor = MonitorContessa()
    
    print("📋 OPCIONES:")
    print("1. Verificación rápida (una sola vez)")
    print("2. Monitoreo continuo (cada 30 segundos)")
    print("3. Monitoreo continuo (cada 10 segundos)")
    print("4. Monitoreo continuo (cada 60 segundos)")
    
    try:
        opcion = input("\nSelecciona opción (1-4) [2]: ").strip() or "2"
        
        if opcion == "1":
            if monitor.verificacion_rapida():
                print("✅ Correos encontrados")
            else:
                print("❌ No hay correos aún")
                
        elif opcion == "2":
            monitor.monitorear_continuo(intervalo_segundos=30, max_intentos=20)
            
        elif opcion == "3":
            monitor.monitorear_continuo(intervalo_segundos=10, max_intentos=30)
            
        elif opcion == "4":
            monitor.monitorear_continuo(intervalo_segundos=60, max_intentos=15)
            
        else:
            print("❌ Opción inválida")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ Detenido por el usuario")
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    print(f"\n🏁 Monitor terminado")


if __name__ == "__main__":
    main()
