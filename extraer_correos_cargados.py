#!/usr/bin/env python3
"""
Script para extraer correos que ya están cargados en el navegador
Usa la sesión activa para obtener el contenido actualizado
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


class ExtractorCorreosCargados(DujawAPI):
    def __init__(self):
        super().__init__()
        
    def extraer_correos_ahora(self):
        """Extrae los correos que ya están cargados"""
        print("📧 EXTRAYENDO CORREOS CARGADOS")
        print("=" * 50)
        
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None
            
        print("✅ Sesión establecida, extrayendo correos...")
        
        # Hacer múltiples intentos para capturar los correos
        correos_encontrados = []
        
        for intento in range(5):
            print(f"\n🔍 Intento {intento + 1}/5 - {datetime.now().strftime('%H:%M:%S')}")
            
            response = self.session.get(self.mailbox_url)
            if response.status_code == 200:
                correos_intento = self.parsear_correos_del_html(response.text, intento + 1)
                
                if correos_intento:
                    correos_encontrados.extend(correos_intento)
                    print(f"✅ Encontrados {len(correos_intento)} elementos en intento {intento + 1}")
                else:
                    print(f"❌ No se encontraron correos en intento {intento + 1}")
                    
                # Guardar HTML de cada intento
                timestamp = datetime.now().strftime("%H%M%S")
                filename = f"mailbox_cargado_{intento+1}_{timestamp}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"💾 HTML guardado: {filename}")
                
            time.sleep(2)  # Esperar entre intentos
            
        return correos_encontrados
        
    def parsear_correos_del_html(self, html_content, intento_num):
        """Parsea correos del HTML con técnicas mejoradas"""
        soup = BeautifulSoup(html_content, 'html.parser')
        correos = []
        
        print(f"🔍 Parseando HTML del intento {intento_num}...")
        
        # 1. Buscar elementos con wire:key (más específico)
        wire_elements = soup.find_all(attrs={'wire:key': True})
        print(f"   📌 Elementos wire:key: {len(wire_elements)}")
        
        for element in wire_elements:
            wire_key = element.get('wire:key', '')
            text = element.get_text(strip=True)
            
            if text and len(text) > 20:
                correos.append({
                    'tipo': 'wire_key',
                    'wire_key': wire_key,
                    'intento': intento_num,
                    'contenido': text,
                    'html': str(element)[:500]
                })
                print(f"   📧 Wire element: {text[:50]}...")
                
        # 2. Buscar tablas (correos suelen estar en tablas)
        tables = soup.find_all('table')
        print(f"   📋 Tablas encontradas: {len(tables)}")
        
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:  # Al menos 2 columnas (típico de emails)
                    row_text = row.get_text(strip=True)
                    if self.parece_correo_real(row_text):
                        correos.append({
                            'tipo': 'table_row',
                            'intento': intento_num,
                            'contenido': row_text,
                            'html': str(row)[:500],
                            'celdas': [cell.get_text(strip=True) for cell in cells]
                        })
                        print(f"   📧 Fila tabla: {row_text[:50]}...")
                        
        # 3. Buscar listas (ul, ol, li)
        lists = soup.find_all(['ul', 'ol'])
        print(f"   📝 Listas encontradas: {len(lists)}")
        
        for lista in lists:
            items = lista.find_all('li')
            for item in items:
                item_text = item.get_text(strip=True)
                if self.parece_correo_real(item_text):
                    correos.append({
                        'tipo': 'list_item',
                        'intento': intento_num,
                        'contenido': item_text,
                        'html': str(item)[:500]
                    })
                    print(f"   📧 Item lista: {item_text[:50]}...")
                    
        # 4. Buscar divs con clases específicas de email
        email_classes = ['message', 'email', 'mail', 'inbox-item', 'msg', 'letter']
        for class_name in email_classes:
            elements = soup.find_all('div', class_=lambda x: x and class_name in str(x).lower())
            for element in elements:
                text = element.get_text(strip=True)
                if self.parece_correo_real(text):
                    correos.append({
                        'tipo': f'div_{class_name}',
                        'intento': intento_num,
                        'contenido': text,
                        'html': str(element)[:500]
                    })
                    print(f"   📧 Div {class_name}: {text[:50]}...")
                    
        # 5. Buscar elementos que contengan direcciones de email
        email_pattern_elements = soup.find_all(text=lambda text: text and '@' in text and '.' in text and len(text) > 10)
        for text_node in email_pattern_elements:
            parent = text_node.parent
            if parent:
                parent_text = parent.get_text(strip=True)
                if self.parece_correo_real(parent_text):
                    correos.append({
                        'tipo': 'email_pattern',
                        'intento': intento_num,
                        'contenido': parent_text,
                        'html': str(parent)[:500]
                    })
                    print(f"   📧 Email pattern: {parent_text[:50]}...")
                    
        # 6. Buscar scripts con datos JSON
        scripts = soup.find_all('script')
        for script in scripts:
            script_text = script.get_text()
            if 'message' in script_text.lower() or 'email' in script_text.lower():
                # Buscar objetos JSON en el script
                import re
                json_objects = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_text)
                for json_str in json_objects:
                    try:
                        data = json.loads(json_str)
                        if self.json_contiene_correo(data):
                            correos.append({
                                'tipo': 'script_json',
                                'intento': intento_num,
                                'contenido': str(data),
                                'data': data
                            })
                            print(f"   📧 Script JSON: {str(data)[:50]}...")
                    except:
                        continue
                        
        print(f"   📊 Total elementos encontrados en intento {intento_num}: {len(correos)}")
        return correos
        
    def parece_correo_real(self, text):
        """Determina si un texto parece ser un correo real (más estricto)"""
        if not text or len(text) < 30:
            return False
            
        text_lower = text.lower()
        
        # Indicadores positivos
        indicadores_positivos = [
            '@' in text and '.' in text,  # Dirección de email
            'subject:' in text_lower,
            'from:' in text_lower,
            'to:' in text_lower,
            'date:' in text_lower,
            'sender:' in text_lower,
            'received:' in text_lower,
            'reply' in text_lower,
            'message' in text_lower and len(text) > 50
        ]
        
        # Indicadores negativos (elementos de interfaz)
        indicadores_negativos = [
            'fetching' in text_lower,
            'loading' in text_lower,
            'waiting' in text_lower,
            'refresh' in text_lower and len(text) < 50,
            'copy' in text_lower and len(text) < 50,
            'delete' in text_lower and len(text) < 50,
            'new' in text_lower and len(text) < 50,
            'button' in text_lower,
            'click' in text_lower
        ]
        
        positivos = sum(indicadores_positivos)
        negativos = sum(indicadores_negativos)
        
        return positivos >= 2 and negativos == 0
        
    def json_contiene_correo(self, data):
        """Verifica si un objeto JSON contiene datos de correo"""
        if not isinstance(data, dict):
            return False
            
        data_str = str(data).lower()
        claves_correo = ['subject', 'from', 'to', 'sender', 'recipient', 'body', 'content', 'message']
        
        return sum(1 for clave in claves_correo if clave in data_str) >= 2
        
    def mostrar_correos_extraidos(self, correos):
        """Muestra los correos extraídos de forma organizada"""
        print(f"\n📧 CORREOS EXTRAÍDOS: {len(correos)}")
        print("=" * 60)
        
        if not correos:
            print("❌ No se encontraron correos")
            print("💡 Posibles razones:")
            print("   - Los correos aún se están cargando")
            print("   - Se necesita JavaScript para cargar el contenido")
            print("   - Los correos están en un formato no detectado")
            return
            
        # Agrupar por tipo
        por_tipo = {}
        for correo in correos:
            tipo = correo['tipo']
            if tipo not in por_tipo:
                por_tipo[tipo] = []
            por_tipo[tipo].append(correo)
            
        for tipo, correos_tipo in por_tipo.items():
            print(f"\n📂 TIPO: {tipo.upper()} ({len(correos_tipo)} elementos)")
            print("-" * 50)
            
            for i, correo in enumerate(correos_tipo, 1):
                print(f"\n  📧 Elemento {i}:")
                print(f"     Intento: {correo['intento']}")
                
                if 'wire_key' in correo:
                    print(f"     Wire Key: {correo['wire_key']}")
                    
                if 'celdas' in correo:
                    print(f"     Celdas: {correo['celdas']}")
                    
                contenido = correo['contenido']
                if len(contenido) > 200:
                    print(f"     Contenido: {contenido[:200]}...")
                else:
                    print(f"     Contenido: {contenido}")
                    
        # Guardar correos extraídos
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_extraidos_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(correos, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Correos extraídos guardados en: {filename}")
        
        # Mostrar resumen
        print(f"\n📊 RESUMEN:")
        print(f"   Total elementos: {len(correos)}")
        print(f"   Tipos diferentes: {len(por_tipo)}")
        print(f"   Elementos más prometedores:")
        
        # Mostrar los 3 elementos más largos (más probable que sean correos reales)
        correos_ordenados = sorted(correos, key=lambda x: len(x['contenido']), reverse=True)
        for i, correo in enumerate(correos_ordenados[:3], 1):
            print(f"     {i}. {correo['tipo']} - {len(correo['contenido'])} chars")


def main():
    """Función principal"""
    print("🚀 EXTRACTOR DE CORREOS CARGADOS")
    print("=" * 60)
    print("📧 Este script extrae correos que ya están cargados en el navegador")
    print("=" * 60)
    
    extractor = ExtractorCorreosCargados()
    
    try:
        correos = extractor.extraer_correos_ahora()
        extractor.mostrar_correos_extraidos(correos)
        
        if correos:
            print(f"\n🎉 ¡Extracción completada!")
            print(f"📁 Revisa los archivos HTML y JSON generados")
        else:
            print(f"\n😞 No se pudieron extraer correos")
            print(f"💡 Intenta ejecutar el script mientras los correos están visibles en el navegador")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        extractor.logout()
        
    print(f"\n🏁 Extracción terminada")


if __name__ == "__main__":
    main()
