#!/usr/bin/env python3
"""
Analizador de datos de red capturados manualmente
Permite importar datos desde herramientas de desarrollador del navegador
"""

import json
import re
import os
from datetime import datetime
from urllib.parse import urlparse, parse_qs
import base64


class ManualCaptureAnalyzer:
    def __init__(self):
        self.requests_data = []
        self.responses_data = []
        
    def import_har_file(self, har_file_path):
        """Importa un archivo HAR (HTTP Archive) desde las herramientas de desarrollador"""
        try:
            with open(har_file_path, 'r', encoding='utf-8') as f:
                har_data = json.load(f)
                
            entries = har_data.get('log', {}).get('entries', [])
            
            for entry in entries:
                request = entry.get('request', {})
                response = entry.get('response', {})
                
                # Procesar request
                request_data = {
                    'timestamp': entry.get('startedDateTime', ''),
                    'method': request.get('method', ''),
                    'url': request.get('url', ''),
                    'headers': {h['name']: h['value'] for h in request.get('headers', [])},
                    'post_data': self.extract_post_data(request),
                    'resource_type': self.guess_resource_type(request.get('url', ''))
                }
                self.requests_data.append(request_data)
                
                # Procesar response
                response_data = {
                    'timestamp': entry.get('startedDateTime', ''),
                    'status': response.get('status', 0),
                    'url': request.get('url', ''),
                    'headers': {h['name']: h['value'] for h in response.get('headers', [])},
                    'content': self.extract_response_content(response),
                    'resource_type': self.guess_resource_type(request.get('url', ''))
                }
                self.responses_data.append(response_data)
                
            print(f"✅ HAR importado: {len(entries)} entradas procesadas")
            return True
            
        except Exception as e:
            print(f"❌ Error importando HAR: {e}")
            return False
            
    def extract_post_data(self, request):
        """Extrae datos POST del request"""
        post_data = request.get('postData', {})
        if post_data:
            text = post_data.get('text', '')
            mime_type = post_data.get('mimeType', '')
            
            if 'application/json' in mime_type:
                try:
                    return json.loads(text)
                except:
                    return text
            else:
                return text
        return None
        
    def extract_response_content(self, response):
        """Extrae contenido de la respuesta"""
        content = response.get('content', {})
        text = content.get('text', '')
        encoding = content.get('encoding', '')
        
        if encoding == 'base64':
            try:
                return base64.b64decode(text).decode('utf-8')
            except:
                return text
        return text
        
    def guess_resource_type(self, url):
        """Adivina el tipo de recurso basado en la URL"""
        if '/api/' in url or url.endswith('.json'):
            return 'xhr'
        elif any(ext in url for ext in ['.js', '.css', '.png', '.jpg', '.gif']):
            return 'other'
        else:
            return 'document'
            
    def import_curl_commands(self, curl_file_path):
        """Importa comandos cURL copiados desde las herramientas de desarrollador"""
        try:
            with open(curl_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Dividir por comandos curl
            curl_commands = re.findall(r'curl[^$]*', content, re.MULTILINE | re.DOTALL)
            
            for curl_cmd in curl_commands:
                request_data = self.parse_curl_command(curl_cmd)
                if request_data:
                    self.requests_data.append(request_data)
                    
            print(f"✅ cURL importado: {len(curl_commands)} comandos procesados")
            return True
            
        except Exception as e:
            print(f"❌ Error importando cURL: {e}")
            return False
            
    def parse_curl_command(self, curl_cmd):
        """Parsea un comando cURL individual"""
        try:
            # Extraer URL
            url_match = re.search(r"curl\s+['\"]([^'\"]+)['\"]", curl_cmd)
            if not url_match:
                url_match = re.search(r"curl\s+([^\s]+)", curl_cmd)
            
            if not url_match:
                return None
                
            url = url_match.group(1)
            
            # Extraer método
            method_match = re.search(r"-X\s+([A-Z]+)", curl_cmd)
            method = method_match.group(1) if method_match else 'GET'
            
            # Extraer headers
            headers = {}
            header_matches = re.findall(r"-H\s+['\"]([^:]+):\s*([^'\"]+)['\"]", curl_cmd)
            for name, value in header_matches:
                headers[name] = value
                
            # Extraer datos POST
            post_data = None
            data_match = re.search(r"--data[^'\"]*['\"]([^'\"]+)['\"]", curl_cmd)
            if data_match:
                data_text = data_match.group(1)
                try:
                    post_data = json.loads(data_text)
                except:
                    post_data = data_text
                    
            return {
                'timestamp': datetime.now().isoformat(),
                'method': method,
                'url': url,
                'headers': headers,
                'post_data': post_data,
                'resource_type': self.guess_resource_type(url)
            }
            
        except Exception as e:
            print(f"❌ Error parseando cURL: {e}")
            return None
            
    def manual_input_session(self):
        """Sesión interactiva para ingresar datos manualmente"""
        print("\n📝 INGRESO MANUAL DE DATOS DE RED")
        print("=" * 40)
        print("Ingresa los datos de las llamadas HTTP que quieres automatizar")
        print("Presiona Enter vacío en URL para terminar")
        
        while True:
            print("\n🔗 Nueva llamada HTTP:")
            url = input("URL: ").strip()
            if not url:
                break
                
            method = input("Método (GET/POST/PUT/DELETE) [GET]: ").strip().upper() or 'GET'
            
            # Headers
            headers = {}
            print("Headers (presiona Enter vacío para terminar):")
            while True:
                header = input("  Header (nombre: valor): ").strip()
                if not header:
                    break
                if ':' in header:
                    name, value = header.split(':', 1)
                    headers[name.strip()] = value.strip()
                    
            # Datos POST
            post_data = None
            if method in ['POST', 'PUT', 'PATCH']:
                data_input = input("Datos JSON (opcional): ").strip()
                if data_input:
                    try:
                        post_data = json.loads(data_input)
                    except:
                        post_data = data_input
                        
            request_data = {
                'timestamp': datetime.now().isoformat(),
                'method': method,
                'url': url,
                'headers': headers,
                'post_data': post_data,
                'resource_type': self.guess_resource_type(url)
            }
            
            self.requests_data.append(request_data)
            print(f"✅ Llamada agregada: {method} {url}")
            
        print(f"\n📊 Total de llamadas ingresadas: {len(self.requests_data)}")
        
    def save_data(self):
        """Guarda los datos procesados"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Guardar requests
        requests_file = f"network_requests_{timestamp}.json"
        with open(requests_file, 'w', encoding='utf-8') as f:
            json.dump(self.requests_data, f, indent=2, ensure_ascii=False)
            
        # Guardar responses
        responses_file = f"network_responses_{timestamp}.json"
        with open(responses_file, 'w', encoding='utf-8') as f:
            json.dump(self.responses_data, f, indent=2, ensure_ascii=False)
            
        print(f"💾 Datos guardados:")
        print(f"   📤 Requests: {requests_file}")
        print(f"   📥 Responses: {responses_file}")
        
        return requests_file, responses_file
        
    def analyze_data(self):
        """Analiza los datos capturados"""
        print(f"\n🔍 ANÁLISIS DE DATOS:")
        print("=" * 30)
        print(f"Total requests: {len(self.requests_data)}")
        print(f"Total responses: {len(self.responses_data)}")
        
        # Agrupar por dominio
        domains = {}
        for req in self.requests_data:
            domain = urlparse(req['url']).netloc
            domains[domain] = domains.get(domain, 0) + 1
            
        print(f"\n🌐 Dominios encontrados:")
        for domain, count in domains.items():
            print(f"   {domain}: {count} llamadas")
            
        # Mostrar APIs detectadas
        api_calls = [req for req in self.requests_data if req['resource_type'] == 'xhr']
        print(f"\n🔗 APIs detectadas ({len(api_calls)}):")
        for api in api_calls:
            print(f"   {api['method']} {api['url']}")
            
        return len(api_calls) > 0


def main():
    """Función principal"""
    print("🔧 ANALIZADOR DE CAPTURA MANUAL")
    print("=" * 40)
    
    analyzer = ManualCaptureAnalyzer()
    
    print("\n📋 OPCIONES DE IMPORTACIÓN:")
    print("1. Importar archivo HAR (desde herramientas de desarrollador)")
    print("2. Importar comandos cURL (copiados desde herramientas de desarrollador)")
    print("3. Ingreso manual de datos")
    print("4. Analizar archivos existentes")
    
    try:
        option = int(input("Selecciona opción (1-4): "))
    except:
        option = 3
        
    if option == 1:
        print("\n📁 Para exportar HAR:")
        print("1. Abre herramientas de desarrollador (F12)")
        print("2. Ve a la pestaña Network/Red")
        print("3. Realiza las acciones en el sitio web")
        print("4. Click derecho en la lista de requests")
        print("5. Selecciona 'Save all as HAR with content'")
        
        har_file = input("\n📂 Ruta del archivo HAR: ").strip()
        if os.path.exists(har_file):
            analyzer.import_har_file(har_file)
        else:
            print("❌ Archivo no encontrado")
            
    elif option == 2:
        print("\n📋 Para copiar cURL:")
        print("1. Abre herramientas de desarrollador (F12)")
        print("2. Ve a la pestaña Network/Red")
        print("3. Realiza las acciones en el sitio web")
        print("4. Click derecho en cada request importante")
        print("5. Selecciona 'Copy as cURL'")
        print("6. Pega todos los comandos en un archivo de texto")
        
        curl_file = input("\n📂 Ruta del archivo con comandos cURL: ").strip()
        if os.path.exists(curl_file):
            analyzer.import_curl_commands(curl_file)
        else:
            print("❌ Archivo no encontrado")
            
    elif option == 3:
        analyzer.manual_input_session()
        
    elif option == 4:
        # Buscar archivos existentes
        import glob
        existing_files = glob.glob("network_requests_*.json")
        if existing_files:
            print(f"📁 Archivos encontrados: {existing_files}")
            print("Los datos ya están disponibles para el generador de API")
        else:
            print("❌ No se encontraron archivos de datos existentes")
            
    # Analizar y guardar datos
    if option in [1, 2, 3]:
        has_apis = analyzer.analyze_data()
        if has_apis:
            analyzer.save_data()
            print("\n✅ DATOS PROCESADOS CORRECTAMENTE")
            print("🔄 Ejecuta 'python api_generator.py' para generar código API")
        else:
            print("\n⚠️ No se detectaron llamadas de API")
            print("💡 Asegúrate de capturar las llamadas AJAX/XHR del sitio web")


if __name__ == "__main__":
    main()
