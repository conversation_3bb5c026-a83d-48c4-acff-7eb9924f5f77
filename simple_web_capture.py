#!/usr/bin/env python3
"""
Script simplificado para capturar flujo web usando requests
Versión básica que funciona sin dependencias complejas
"""

import json
import time
import requests
from datetime import datetime
from urllib.parse import urljoin, urlparse
import re


class SimpleWebCapture:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.requests_log = []
        self.base_url = ""
        
    def log_request(self, method, url, headers=None, data=None, response=None):
        """Registra una petición HTTP"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'method': method,
            'url': url,
            'headers': dict(headers) if headers else {},
            'data': data,
            'response_status': response.status_code if response else None,
            'response_headers': dict(response.headers) if response else {},
            'response_content': response.text[:1000] if response and response.text else None  # Primeros 1000 chars
        }
        self.requests_log.append(log_entry)
        print(f"📤 {method} {url} -> {response.status_code if response else 'N/A'}")
        
    def get_page(self, url):
        """Obtiene una página web"""
        try:
            response = self.session.get(url)
            self.log_request('GET', url, self.session.headers, None, response)
            return response
        except Exception as e:
            print(f"❌ Error obteniendo {url}: {e}")
            return None
            
    def post_data(self, url, data=None, json_data=None):
        """Envía datos POST"""
        try:
            if json_data:
                response = self.session.post(url, json=json_data)
                self.log_request('POST', url, self.session.headers, json_data, response)
            else:
                response = self.session.post(url, data=data)
                self.log_request('POST', url, self.session.headers, data, response)
            return response
        except Exception as e:
            print(f"❌ Error enviando POST a {url}: {e}")
            return None
            
    def find_forms(self, html_content):
        """Encuentra formularios en el HTML"""
        forms = []
        # Buscar formularios con regex simple
        form_pattern = r'<form[^>]*>(.*?)</form>'
        form_matches = re.findall(form_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for i, form_content in enumerate(form_matches):
            # Buscar campos de input
            input_pattern = r'<input[^>]*>'
            inputs = re.findall(input_pattern, form_content, re.IGNORECASE)
            
            form_info = {
                'form_index': i,
                'inputs': [],
                'action': '',
                'method': 'GET'
            }
            
            # Extraer información de cada input
            for input_tag in inputs:
                name_match = re.search(r'name=["\']([^"\']*)["\']', input_tag, re.IGNORECASE)
                type_match = re.search(r'type=["\']([^"\']*)["\']', input_tag, re.IGNORECASE)
                
                if name_match:
                    input_info = {
                        'name': name_match.group(1),
                        'type': type_match.group(1) if type_match else 'text'
                    }
                    form_info['inputs'].append(input_info)
                    
            forms.append(form_info)
            
        return forms
        
    def interactive_login(self, login_url):
        """Proceso de login interactivo"""
        print(f"\n🔐 Iniciando proceso de login en: {login_url}")
        
        # Obtener la página de login
        response = self.get_page(login_url)
        if not response:
            return False
            
        # Buscar formularios
        forms = self.find_forms(response.text)
        
        if not forms:
            print("❌ No se encontraron formularios en la página")
            return False
            
        print(f"✅ Encontrados {len(forms)} formularios")
        
        # Mostrar formularios encontrados
        for i, form in enumerate(forms):
            print(f"\n📋 Formulario {i+1}:")
            for input_field in form['inputs']:
                print(f"   - {input_field['name']} ({input_field['type']})")
                
        # Seleccionar formulario
        if len(forms) == 1:
            selected_form = forms[0]
            print(f"🎯 Usando formulario único")
        else:
            try:
                form_index = int(input(f"Selecciona formulario (1-{len(forms)}): ")) - 1
                selected_form = forms[form_index]
            except:
                selected_form = forms[0]
                print("🎯 Usando primer formulario por defecto")
                
        # Recopilar datos del formulario
        form_data = {}
        for input_field in selected_form['inputs']:
            if input_field['type'].lower() == 'password':
                value = input(f"🔒 {input_field['name']} (password): ")
            elif input_field['type'].lower() in ['text', 'email']:
                value = input(f"👤 {input_field['name']}: ")
            elif input_field['type'].lower() == 'hidden':
                # Para campos ocultos, intentar extraer el valor del HTML
                hidden_pattern = f'name=["\']?{re.escape(input_field["name"])}["\']?[^>]*value=["\']([^"\']*)["\']'
                hidden_match = re.search(hidden_pattern, response.text, re.IGNORECASE)
                value = hidden_match.group(1) if hidden_match else ''
                print(f"🔍 Campo oculto {input_field['name']}: {value}")
            else:
                value = input(f"📝 {input_field['name']} ({input_field['type']}): ")
                
            if value:
                form_data[input_field['name']] = value
                
        # Enviar formulario
        print(f"\n📤 Enviando datos de login...")
        login_response = self.post_data(login_url, data=form_data)
        
        if login_response and login_response.status_code in [200, 302]:
            print("✅ Login enviado correctamente")
            return True
        else:
            print(f"❌ Error en login: {login_response.status_code if login_response else 'Sin respuesta'}")
            return False
            
    def manual_navigation_guide(self):
        """Guía para navegación manual"""
        print("\n🧭 GUÍA DE NAVEGACIÓN MANUAL:")
        print("=" * 50)
        print("1. Abre tu navegador web")
        print("2. Ve al sitio web que quieres automatizar")
        print("3. Abre las herramientas de desarrollador (F12)")
        print("4. Ve a la pestaña 'Network' o 'Red'")
        print("5. Realiza las acciones que quieres automatizar")
        print("6. Copia las llamadas HTTP importantes")
        print("7. Guarda la información en un archivo")
        print("\n💡 TIPS:")
        print("- Filtra por XHR/Fetch para ver solo las llamadas de API")
        print("- Copia como cURL para obtener headers y datos")
        print("- Anota las URLs y parámetros importantes")
        
    def save_captured_data(self):
        """Guarda los datos capturados"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"captured_requests_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.requests_log, f, indent=2, ensure_ascii=False)
            
        print(f"💾 Datos guardados en: {filename}")
        return filename
        
    def analyze_captured_data(self):
        """Analiza los datos capturados"""
        print(f"\n🔍 ANÁLISIS DE DATOS CAPTURADOS:")
        print("=" * 40)
        print(f"Total de requests: {len(self.requests_log)}")
        
        # Agrupar por método
        methods = {}
        for req in self.requests_log:
            method = req['method']
            methods[method] = methods.get(method, 0) + 1
            
        print("\n📊 Métodos HTTP:")
        for method, count in methods.items():
            print(f"   {method}: {count}")
            
        # Mostrar URLs únicas
        unique_urls = set(req['url'] for req in self.requests_log)
        print(f"\n🔗 URLs únicas: {len(unique_urls)}")
        for url in sorted(unique_urls):
            print(f"   {url}")


def main():
    """Función principal"""
    print("🚀 CAPTURADOR WEB SIMPLIFICADO")
    print("=" * 40)
    
    capture = SimpleWebCapture()
    
    # Obtener URL del sitio
    site_url = input("🌐 Ingresa la URL del sitio web: ").strip()
    if not site_url.startswith(('http://', 'https://')):
        site_url = 'https://' + site_url
        
    capture.base_url = site_url
    
    print(f"\n🎯 Trabajando con: {site_url}")
    
    # Opciones de captura
    print("\n📋 OPCIONES DE CAPTURA:")
    print("1. Captura automática básica (GET de la página principal)")
    print("2. Login interactivo + captura")
    print("3. Guía de navegación manual")
    
    try:
        option = int(input("Selecciona opción (1-3): "))
    except:
        option = 1
        
    if option == 1:
        print("\n🔄 Realizando captura básica...")
        response = capture.get_page(site_url)
        if response:
            print("✅ Página principal obtenida")
            
    elif option == 2:
        print("\n🔐 Iniciando proceso de login...")
        login_success = capture.interactive_login(site_url)
        if login_success:
            # Después del login, permitir más navegación
            print("\n🧭 ¿Quieres hacer más peticiones? (y/n)")
            if input().lower().strip() == 'y':
                while True:
                    next_url = input("🔗 URL siguiente (o 'exit' para terminar): ").strip()
                    if next_url.lower() == 'exit':
                        break
                    if not next_url.startswith(('http://', 'https://')):
                        next_url = urljoin(site_url, next_url)
                    capture.get_page(next_url)
                    
    elif option == 3:
        capture.manual_navigation_guide()
        input("\nPresiona Enter cuando hayas terminado la navegación manual...")
        
    # Analizar y guardar datos
    capture.analyze_captured_data()
    filename = capture.save_captured_data()
    
    print(f"\n✅ CAPTURA COMPLETADA")
    print(f"📁 Archivo generado: {filename}")
    print("\n🔄 Ejecuta 'python api_generator.py' para generar código API")


if __name__ == "__main__":
    main()
