#!/usr/bin/env python3
"""
Monitor simple para el mailbox de Contessa
"""

import requests
import json
import time
import re
from datetime import datetime
from bs4 import BeautifulSoup


class SimpleContessaMonitor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.base_url = "https://dujaw.com"
        self.mailbox_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.password = "unlockgs2024"
        self.csrf_token = None
        
    def get_csrf_token(self):
        """Obtiene el token CSRF"""
        try:
            response = self.session.get(self.mailbox_url)
            if response.status_code == 200:
                # Buscar token CSRF en el HTML
                csrf_match = re.search(r'name="csrf-token" content="([^"]+)"', response.text)
                if csrf_match:
                    self.csrf_token = csrf_match.group(1)
                    return True
                    
                # Buscar en meta tag
                csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', response.text)
                if csrf_match:
                    self.csrf_token = csrf_match.group(1)
                    return True
                    
        except Exception as e:
            print(f"❌ Error obteniendo CSRF: {e}")
            
        return False
        
    def unlock_mailbox(self):
        """Hace unlock del mailbox"""
        try:
            print("🔓 Haciendo unlock...")
            
            if not self.get_csrf_token():
                print("❌ No se pudo obtener token CSRF")
                return False
                
            # Datos para unlock
            unlock_data = {
                'password': self.password,
                '_token': self.csrf_token
            }
            
            # Headers para unlock
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRF-TOKEN': self.csrf_token,
                'Referer': self.mailbox_url
            }
            
            response = self.session.post(self.mailbox_url, data=unlock_data, headers=headers)
            
            if response.status_code == 200:
                print("✅ Unlock exitoso")
                return True
            else:
                print(f"❌ Error unlock: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error en unlock: {e}")
            return False
            
    def verificar_correos_livewire(self):
        """Verifica correos usando Livewire"""
        try:
            # Obtener página principal
            response = self.session.get(self.mailbox_url)
            if response.status_code != 200:
                return []
                
            html_content = response.text
            
            # Buscar componente Livewire
            app_pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"[^>]*'
            
            for match in re.finditer(app_pattern, html_content):
                wire_id = match.group(1)
                initial_data_encoded = match.group(2)
                
                if 'frontend.app' in initial_data_encoded:
                    # Decodificar datos
                    import html
                    initial_data_json = html.unescape(initial_data_encoded)
                    
                    try:
                        initial_data = json.loads(initial_data_json)
                        messages = initial_data['serverMemo']['data']['messages']
                        
                        if len(messages) > 0:
                            print(f"🎉 {len(messages)} mensajes encontrados!")
                            return messages
                            
                        # Hacer llamada fetchMessages
                        return self.fetch_messages_livewire(wire_id, initial_data)
                        
                    except json.JSONDecodeError:
                        continue
                        
            return []
            
        except Exception as e:
            print(f"❌ Error verificando correos: {e}")
            return []
            
    def fetch_messages_livewire(self, wire_id, initial_data):
        """Hace llamada fetchMessages a Livewire"""
        try:
            livewire_request = {
                'fingerprint': initial_data['fingerprint'],
                'serverMemo': initial_data['serverMemo'],
                'updates': [
                    {
                        'type': 'fireEvent',
                        'payload': {
                            'id': wire_id,
                            'event': 'fetchMessages',
                            'params': []
                        }
                    }
                ]
            }
            
            headers = {
                'X-Livewire': 'true',
                'X-CSRF-TOKEN': self.csrf_token,
                'Content-Type': 'application/json',
                'Accept': 'text/html, application/xhtml+xml',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': self.mailbox_url
            }
            
            livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
            
            response = self.session.post(livewire_url, json=livewire_request, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                # Buscar mensajes en serverMemo
                if 'serverMemo' in data and 'data' in data['serverMemo']:
                    server_data = data['serverMemo']['data']
                    if 'messages' in server_data:
                        messages = server_data['messages']
                        if len(messages) > 0:
                            print(f"🎉 {len(messages)} mensajes obtenidos via Livewire!")
                            return messages
                            
                # Buscar en effects.html
                if 'effects' in data and 'html' in data['effects']:
                    html_content = data['effects']['html']
                    links = self.extraer_links_del_html(html_content)
                    if links:
                        print(f"🎉 {len(links)} links encontrados en HTML!")
                        return [{'html_content': html_content, 'links': links}]
                        
            return []
            
        except Exception as e:
            print(f"❌ Error en fetchMessages: {e}")
            return []
            
    def extraer_links_del_html(self, html_content):
        """Extrae links de aprobación del HTML"""
        links = []
        
        # Patrón para links de aprobación
        pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        
        for match in matches:
            links.append(match)
            
        return list(set(links))  # Remover duplicados
        
    def seguir_link(self, link):
        """Sigue un link de aprobación"""
        try:
            print(f"🔗 Siguiendo link: {link}")
            
            response = self.session.get(link, timeout=30)
            
            print(f"📥 Respuesta: {response.status_code}")
            
            if response.status_code == 200:
                # Analizar contenido
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Buscar título
                title = soup.find('title')
                if title:
                    print(f"📋 Título: {title.get_text(strip=True)}")
                    
                # Buscar encabezados
                for i in range(1, 4):
                    headings = soup.find_all(f'h{i}')
                    for heading in headings:
                        text = heading.get_text(strip=True)
                        if text:
                            print(f"📌 H{i}: {text}")
                            
                # Buscar mensajes importantes
                text_content = soup.get_text().lower()
                
                if 'invalid' in text_content or 'expired' in text_content or 'timed out' in text_content:
                    print("❌ Link expirado o inválido")
                elif 'approve' in text_content:
                    print("✅ Página de aprobación activa")
                else:
                    print("❓ Estado del link incierto")
                    
                # Guardar HTML
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"contessa_link_page_{timestamp}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"💾 Página guardada: {filename}")
                
                return True
                
            else:
                print(f"❌ Error accediendo link: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error siguiendo link: {e}")
            return False
            
    def monitorear(self, intervalo=15, max_intentos=40):
        """Monitorea el mailbox continuamente"""
        print("🔄 MONITOREANDO MAILBOX CONTESSA")
        print("=" * 50)
        print(f"📧 Email: <EMAIL>")
        print(f"⏱️ Intervalo: {intervalo} segundos")
        print(f"🔢 Máximo: {max_intentos} intentos")
        print("=" * 50)
        
        for intento in range(max_intentos):
            print(f"\n🔍 INTENTO {intento + 1}/{max_intentos} - {datetime.now().strftime('%H:%M:%S')}")
            
            try:
                # Unlock
                if self.unlock_mailbox():
                    # Verificar correos
                    mensajes = self.verificar_correos_livewire()
                    
                    if mensajes:
                        print(f"🎉 ¡CORREOS ENCONTRADOS!")
                        
                        # Procesar mensajes
                        links_encontrados = []
                        
                        for mensaje in mensajes:
                            if isinstance(mensaje, dict):
                                # Buscar links en el mensaje
                                if 'html_content' in mensaje:
                                    links_encontrados.extend(mensaje.get('links', []))
                                elif 'body' in mensaje:
                                    body = mensaje['body']
                                    pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
                                    links = re.findall(pattern, body, re.IGNORECASE)
                                    links_encontrados.extend(links)
                                    
                                # Mostrar info del mensaje
                                if 'subject' in mensaje:
                                    print(f"📋 Asunto: {mensaje['subject']}")
                                if 'from' in mensaje:
                                    print(f"👤 De: {mensaje['from']}")
                                    
                        # Seguir primer link encontrado
                        if links_encontrados:
                            print(f"\n🔗 {len(links_encontrados)} links encontrados:")
                            for i, link in enumerate(links_encontrados, 1):
                                print(f"  {i}. {link}")
                                
                            # Seguir el primer link
                            primer_link = links_encontrados[0]
                            print(f"\n🔗 Siguiendo primer link...")
                            self.seguir_link(primer_link)
                            
                            # Guardar resultados
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            resultado = {
                                'timestamp': datetime.now().isoformat(),
                                'email': '<EMAIL>',
                                'mensajes': mensajes,
                                'links': links_encontrados,
                                'link_seguido': primer_link
                            }
                            
                            filename = f"contessa_resultado_{timestamp}.json"
                            with open(filename, 'w', encoding='utf-8') as f:
                                json.dump(resultado, f, indent=2, ensure_ascii=False)
                            print(f"💾 Resultado guardado: {filename}")
                            
                            return True
                        else:
                            print("⚠️ Mensajes encontrados pero sin links de aprobación")
                    else:
                        print("📭 Sin mensajes aún")
                else:
                    print("❌ Error en unlock")
                    
            except Exception as e:
                print(f"❌ Error en intento {intento + 1}: {e}")
                
            # Esperar antes del siguiente intento
            if intento < max_intentos - 1:
                print(f"⏳ Esperando {intervalo} segundos...")
                try:
                    time.sleep(intervalo)
                except KeyboardInterrupt:
                    print("\n⏹️ Detenido por el usuario")
                    return False
                    
        print(f"\n😞 No se encontraron correos después de {max_intentos} intentos")
        return False


def main():
    """Función principal"""
    print("🚀 MONITOR SIMPLE CONTESSA")
    print("=" * 40)
    
    monitor = SimpleContessaMonitor()
    
    try:
        # Monitorear cada 15 segundos, máximo 40 intentos (10 minutos)
        monitor.monitorear(intervalo=15, max_intentos=40)
        
    except KeyboardInterrupt:
        print("\n⏹️ Detenido por el usuario")
    except Exception as e:
        print(f"❌ Error: {e}")
        
    print("\n🏁 Monitor terminado")


if __name__ == "__main__":
    main()
