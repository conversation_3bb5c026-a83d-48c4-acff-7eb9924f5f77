#!/usr/bin/env python3
"""
Script para acceder al <NAME_EMAIL> y autorizar el cambio
"""

import requests
import json
import re
import time
from datetime import datetime
from bs4 import BeautifulSoup


class DujawContessaReader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.base_url = "https://dujaw.com"
        self.mailbox_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.password = "unlockgs2024"
        self.csrf_token = None
        
    def get_csrf_token(self):
        """Obtiene el token CSRF"""
        try:
            response = self.session.get(self.mailbox_url)
            if response.status_code == 200:
                # Buscar token CSRF
                csrf_match = re.search(r'name="csrf-token" content="([^"]+)"', response.text)
                if csrf_match:
                    self.csrf_token = csrf_match.group(1)
                    return True
                    
                csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', response.text)
                if csrf_match:
                    self.csrf_token = csrf_match.group(1)
                    return True
                    
        except Exception as e:
            print(f"❌ Error obteniendo CSRF: {e}")
            
        return False
        
    def unlock_mailbox(self):
        """Hace unlock del mailbox"""
        try:
            print("🔓 Haciendo unlock del mailbox dujaw...")
            
            if not self.get_csrf_token():
                print("❌ No se pudo obtener token CSRF")
                return False
                
            unlock_data = {
                'password': self.password,
                '_token': self.csrf_token
            }
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRF-TOKEN': self.csrf_token,
                'Referer': self.mailbox_url
            }
            
            response = self.session.post(self.mailbox_url, data=unlock_data, headers=headers)
            
            if response.status_code == 200:
                print("✅ Unlock exitoso")
                return True
            else:
                print(f"❌ Error unlock: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error en unlock: {e}")
            return False
            
    def obtener_correos_livewire(self):
        """Obtiene correos usando Livewire"""
        try:
            print("🔍 Obteniendo correos via Livewire...")
            
            # Obtener página principal
            response = self.session.get(self.mailbox_url)
            if response.status_code != 200:
                return []
                
            html_content = response.text
            
            # Guardar HTML para debug
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            html_filename = f"dujaw_contessa_{timestamp}.html"
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"💾 HTML guardado: {html_filename}")
            
            # Buscar componente Livewire
            app_pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"[^>]*'
            
            for match in re.finditer(app_pattern, html_content):
                wire_id = match.group(1)
                initial_data_encoded = match.group(2)
                
                if 'frontend.app' in initial_data_encoded:
                    # Decodificar datos
                    import html
                    initial_data_json = html.unescape(initial_data_encoded)
                    
                    try:
                        initial_data = json.loads(initial_data_json)
                        messages = initial_data['serverMemo']['data']['messages']
                        
                        print(f"📧 Mensajes iniciales encontrados: {len(messages)}")
                        
                        if len(messages) > 0:
                            return messages
                            
                        # Hacer llamada fetchMessages
                        return self.fetch_messages_livewire(wire_id, initial_data)
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ Error decodificando JSON: {e}")
                        continue
                        
            return []
            
        except Exception as e:
            print(f"❌ Error obteniendo correos: {e}")
            return []
            
    def fetch_messages_livewire(self, wire_id, initial_data):
        """Hace llamada fetchMessages a Livewire"""
        try:
            print("🔄 Ejecutando fetchMessages...")
            
            livewire_request = {
                'fingerprint': initial_data['fingerprint'],
                'serverMemo': initial_data['serverMemo'],
                'updates': [
                    {
                        'type': 'fireEvent',
                        'payload': {
                            'id': wire_id,
                            'event': 'fetchMessages',
                            'params': []
                        }
                    }
                ]
            }
            
            headers = {
                'X-Livewire': 'true',
                'X-CSRF-TOKEN': self.csrf_token,
                'Content-Type': 'application/json',
                'Accept': 'text/html, application/xhtml+xml',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': self.mailbox_url
            }
            
            livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
            
            response = self.session.post(livewire_url, json=livewire_request, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                # Buscar mensajes en serverMemo
                if 'serverMemo' in data and 'data' in data['serverMemo']:
                    server_data = data['serverMemo']['data']
                    if 'messages' in server_data:
                        messages = server_data['messages']
                        if len(messages) > 0:
                            print(f"🎉 {len(messages)} mensajes obtenidos!")
                            return messages
                            
                # Buscar en effects.html
                if 'effects' in data and 'html' in data['effects']:
                    html_content = data['effects']['html']
                    
                    # Guardar HTML de effects
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    effects_filename = f"dujaw_contessa_effects_{timestamp}.html"
                    with open(effects_filename, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    print(f"💾 Effects HTML guardado: {effects_filename}")
                    
                    # Buscar links en el HTML
                    links = self.extraer_links_del_html(html_content)
                    if links:
                        print(f"🎉 {len(links)} links encontrados!")
                        return [{'html_content': html_content, 'links': links}]
                        
            return []
            
        except Exception as e:
            print(f"❌ Error en fetchMessages: {e}")
            return []
            
    def extraer_links_del_html(self, html_content):
        """Extrae links de aprobación del HTML"""
        links = []
        
        # Patrón para links de aprobación de Pokémon
        pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        
        for match in matches:
            links.append(match)
            
        return list(set(links))  # Remover duplicados
        
    def seguir_link_autorizacion(self, link):
        """Sigue un link de autorización y lo completa"""
        print(f"\n🔗 SIGUIENDO LINK DE AUTORIZACIÓN:")
        print(f"   {link}")
        
        try:
            # Crear nueva sesión para Pokémon
            pokemon_session = requests.Session()
            pokemon_session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            # Acceder al link
            response = pokemon_session.get(link, timeout=30)
            
            print(f"📥 Respuesta: {response.status_code}")
            
            if response.status_code == 200:
                # Guardar página
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"pokemon_auth_page_{timestamp}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"💾 Página guardada: {filename}")
                
                # Analizar contenido
                soup = BeautifulSoup(response.text, 'html.parser')
                text_content = soup.get_text().lower()
                
                if 'invalid' in text_content or 'expired' in text_content:
                    print("❌ Link expirado o inválido")
                    return False
                elif 'approve' in text_content or 'authorize' in text_content:
                    print("✅ Página de autorización activa")
                    
                    # Buscar botones de autorización
                    buttons = soup.find_all(['button', 'a'], string=re.compile(r'approve|authorize|confirm', re.I))
                    
                    if buttons:
                        print(f"🔘 {len(buttons)} botones de autorización encontrados")
                        
                        for button in buttons:
                            button_text = button.get_text(strip=True)
                            print(f"   🔘 {button_text}")
                            
                            # Si es un link, seguirlo
                            if button.name == 'a' and button.get('href'):
                                auth_link = button.get('href')
                                
                                # Construir URL completa
                                if auth_link.startswith('/'):
                                    auth_link = f"https://club.pokemon.com{auth_link}"
                                elif not auth_link.startswith('http'):
                                    auth_link = f"https://club.pokemon.com/us/pokemon-trainer-club/{auth_link}"
                                
                                print(f"🔗 Siguiendo link de autorización: {auth_link}")
                                
                                auth_response = pokemon_session.get(auth_link, timeout=30)
                                
                                if auth_response.status_code == 200:
                                    # Guardar resultado
                                    result_filename = f"pokemon_auth_result_{timestamp}.html"
                                    with open(result_filename, 'w', encoding='utf-8') as f:
                                        f.write(auth_response.text)
                                    print(f"💾 Resultado guardado: {result_filename}")
                                    
                                    # Analizar resultado
                                    result_soup = BeautifulSoup(auth_response.text, 'html.parser')
                                    result_text = result_soup.get_text().lower()
                                    
                                    if 'success' in result_text or 'authorized' in result_text or 'approved' in result_text:
                                        print("🎉 ¡AUTORIZACIÓN EXITOSA!")
                                        return True
                                    else:
                                        print("❓ Resultado de autorización incierto")
                                        return None
                                else:
                                    print(f"❌ Error en autorización: {auth_response.status_code}")
                                    
                        return None
                    else:
                        print("⚠️ No se encontraron botones de autorización")
                        return None
                else:
                    print("❓ Contenido de página incierto")
                    return None
                    
            else:
                print(f"❌ Error accediendo link: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error siguiendo link: {e}")
            return False
            
    def procesar_mailbox_completo(self):
        """Procesa completamente el mailbox de dujaw"""
        print("📧 PROCESANDO MAILBOX DUJAW CONTESSA")
        print("=" * 60)
        print(f"📧 Email: <EMAIL>")
        print(f"🔑 Password: {self.password}")
        print("=" * 60)
        
        # 1. Unlock
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return False
            
        # 2. Obtener correos
        mensajes = self.obtener_correos_livewire()
        
        if not mensajes:
            print("📭 No se encontraron mensajes")
            return False
            
        print(f"📧 {len(mensajes)} mensajes encontrados")
        
        # 3. Buscar links de autorización
        links_autorizacion = []
        
        for mensaje in mensajes:
            if isinstance(mensaje, dict):
                # Buscar en contenido del mensaje
                if 'body' in mensaje:
                    body = mensaje['body']
                    links = self.extraer_links_del_html(body)
                    links_autorizacion.extend(links)
                    
                if 'content' in mensaje:
                    content = mensaje['content']
                    links = self.extraer_links_del_html(content)
                    links_autorizacion.extend(links)
                    
                # Buscar en HTML content
                if 'html_content' in mensaje:
                    links = mensaje.get('links', [])
                    links_autorizacion.extend(links)
                    
                # Mostrar info del mensaje
                if 'subject' in mensaje:
                    print(f"📋 Asunto: {mensaje['subject']}")
                if 'from' in mensaje:
                    print(f"👤 De: {mensaje['from']}")
                if 'date' in mensaje:
                    print(f"📅 Fecha: {mensaje['date']}")
                    
        # Remover duplicados
        links_autorizacion = list(set(links_autorizacion))
        
        if links_autorizacion:
            print(f"\n🔗 {len(links_autorizacion)} LINKS DE AUTORIZACIÓN ENCONTRADOS:")
            for i, link in enumerate(links_autorizacion, 1):
                print(f"   {i}. {link}")
                
            # Seguir el primer link
            primer_link = links_autorizacion[0]
            print(f"\n🚀 Siguiendo primer link de autorización...")
            
            resultado = self.seguir_link_autorizacion(primer_link)
            
            if resultado is True:
                print(f"\n🎉 ¡AUTORIZACIÓN COMPLETADA EXITOSAMENTE!")
                return True
            elif resultado is False:
                print(f"\n❌ Error en la autorización")
                return False
            else:
                print(f"\n❓ Resultado de autorización incierto")
                return None
        else:
            print(f"\n❌ No se encontraron links de autorización")
            return False


def main():
    """Función principal"""
    print("🚀 LECTOR MAILBOX DUJAW CONTESSA")
    print("=" * 50)
    
    reader = DujawContessaReader()
    
    try:
        resultado = reader.procesar_mailbox_completo()
        
        print(f"\n📊 RESULTADO FINAL:")
        if resultado is True:
            print("🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
            print("✅ Autorización realizada correctamente")
        elif resultado is False:
            print("❌ PROCESO FALLIDO")
            print("💡 Revisa los archivos generados para más detalles")
        else:
            print("❓ RESULTADO INCIERTO")
            print("💡 Requiere verificación manual")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    print(f"\n🏁 Proceso terminado")


if __name__ == "__main__":
    main()
