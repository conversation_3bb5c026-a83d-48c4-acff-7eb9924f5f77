#!/usr/bin/env python3
"""
Script para completar automáticamente el cambio de email de Contessa
"""

import requests
from bs4 import BeautifulSoup
from datetime import datetime


def completar_cambio_email():
    """Completa automáticamente el cambio de email"""
    link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/dcfe346f2983da54fd931425552a3b8e"
    password = "Cg#ruCZ7B"
    
    print("🚀 COMPLETANDO CAMBIO DE EMAIL AUTOMÁTICAMENTE")
    print("=" * 60)
    print(f"📧 Email actual: <EMAIL>")
    print(f"📧 Email nuevo: <EMAIL>")
    print(f"🔗 Link: {link}")
    print(f"🔑 Password: {'*' * len(password)}")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    try:
        # Paso 1: Obtener la página de aprobación
        print("🌐 Paso 1: Accediendo a la página de aprobación...")
        response = session.get(link, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ Error accediendo al link: {response.status_code}")
            return False
            
        print("✅ Página cargada exitosamente")
        
        # Paso 2: Parsear el formulario
        print("🔍 Paso 2: Analizando formulario...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar el formulario de confirmación (el que tiene los campos de email)
        target_form = None
        for form in soup.find_all('form'):
            inputs = form.find_all('input')
            input_names = [inp.get('name', '') for inp in inputs]
            
            if 'current_email' in input_names and 'new_email' in input_names:
                target_form = form
                break
                
        if not target_form:
            print("❌ No se encontró el formulario de confirmación")
            return False
            
        print("✅ Formulario de confirmación encontrado")
        
        # Paso 3: Extraer datos del formulario
        print("📝 Paso 3: Extrayendo datos del formulario...")
        
        action = target_form.get('action', '')
        method = target_form.get('method', 'POST').upper()
        
        # Construir URL del formulario
        if action.startswith('http'):
            form_url = action
        elif action.startswith('/'):
            form_url = f"https://club.pokemon.com{action}"
        else:
            form_url = link  # Usar la misma URL si action está vacío
            
        print(f"🔗 URL del formulario: {form_url}")
        print(f"📤 Método: {method}")
        
        # Extraer todos los campos del formulario
        form_data = {}
        
        inputs = target_form.find_all(['input', 'select', 'textarea'])
        for input_elem in inputs:
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', '')
            
            if name:
                if input_type == 'checkbox':
                    if input_elem.has_attr('checked'):
                        form_data[name] = value or 'on'
                elif input_type == 'radio':
                    if input_elem.has_attr('checked'):
                        form_data[name] = value
                elif input_type not in ['submit', 'button', 'reset']:
                    form_data[name] = value
        
        # Agregar la contraseña
        form_data['current_password'] = password
        
        print("📋 Datos del formulario:")
        for key, value in form_data.items():
            if 'password' in key.lower():
                print(f"   🔑 {key}: {'*' * len(value)}")
            elif 'token' in key.lower() or 'csrf' in key.lower():
                print(f"   🔒 {key}: {value[:20]}...")
            else:
                print(f"   📝 {key}: {value}")
        
        # Paso 4: Buscar y agregar el botón de confirmación
        print("🔍 Paso 4: Buscando botón de confirmación...")
        
        confirm_buttons = target_form.find_all(['input', 'button'], 
                                             attrs={'type': ['submit', 'button']})
        
        confirm_button = None
        for button in confirm_buttons:
            button_text = (button.get('value', '') + ' ' + button.get_text(strip=True)).lower()
            if any(word in button_text for word in ['confirm', 'approve', 'submit']):
                confirm_button = button
                break
        
        if confirm_button:
            button_name = confirm_button.get('name')
            button_value = confirm_button.get('value', '')
            
            if button_name:
                form_data[button_name] = button_value
                
            print(f"✅ Botón encontrado: {button_value or confirm_button.get_text(strip=True)}")
        else:
            print("⚠️ No se encontró botón específico, enviando formulario sin botón")
        
        # Paso 5: Enviar el formulario
        print("🚀 Paso 5: Enviando formulario de aprobación...")
        
        # Headers adicionales para el envío
        headers = {
            'Referer': link,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://club.pokemon.com'
        }
        
        if method == 'POST':
            response = session.post(form_url, data=form_data, headers=headers, timeout=30)
        else:
            response = session.get(form_url, params=form_data, headers=headers, timeout=30)
        
        print(f"📥 Respuesta del servidor: {response.status_code}")
        
        # Paso 6: Analizar el resultado
        print("🔍 Paso 6: Analizando resultado...")
        
        if response.status_code == 200:
            # Guardar respuesta completa
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"cambio_email_resultado_{timestamp}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 Respuesta guardada: {filename}")
            
            # Analizar contenido
            soup = BeautifulSoup(response.text, 'html.parser')
            text_content = soup.get_text().lower()
            
            # Buscar indicadores de éxito
            success_indicators = [
                'success', 'successful', 'approved', 'confirmed', 'complete',
                'email has been changed', 'change has been approved',
                'your email address has been updated'
            ]
            
            # Buscar indicadores de error
            error_indicators = [
                'error', 'invalid', 'incorrect', 'failed', 'expired',
                'wrong password', 'authentication failed'
            ]
            
            success_found = any(indicator in text_content for indicator in success_indicators)
            error_found = any(indicator in text_content for indicator in error_indicators)
            
            # Buscar mensajes específicos en la página
            messages = []
            
            # Buscar divs con clases de mensaje
            message_elements = soup.find_all(['div', 'p', 'span'], 
                                           class_=lambda x: x and any(cls in str(x).lower() 
                                           for cls in ['message', 'alert', 'notification', 'error', 'success']))
            
            for element in message_elements:
                text = element.get_text(strip=True)
                if text and len(text) > 10:
                    messages.append(text)
            
            # Buscar título de la página
            title = soup.find('title')
            page_title = title.get_text(strip=True) if title else "Sin título"
            
            print(f"📋 Título de la página: {page_title}")
            
            if messages:
                print(f"💬 Mensajes encontrados:")
                for i, message in enumerate(messages, 1):
                    print(f"   {i}. {message}")
            
            # Determinar resultado
            if success_found and not error_found:
                print("🎉 ¡CAMBIO DE EMAIL EXITOSO!")
                print("✅ El email ha sido cambiado correctamente")
                return True
            elif error_found:
                print("❌ ERROR EN EL CAMBIO DE EMAIL")
                if 'password' in text_content or 'authentication' in text_content:
                    print("🔑 Posible error de contraseña")
                elif 'expired' in text_content:
                    print("⏰ Link expirado")
                else:
                    print("❓ Error no especificado")
                return False
            else:
                print("❓ RESULTADO INCIERTO")
                print("💡 Revisa el archivo HTML guardado para más detalles")
                
                # Buscar formularios en la respuesta
                response_forms = soup.find_all('form')
                if response_forms:
                    print("📝 La página contiene formularios adicionales")
                
                return None
                
        elif response.status_code == 302 or response.status_code == 301:
            print("🔄 Redirección detectada")
            
            # Seguir redirección
            if 'Location' in response.headers:
                redirect_url = response.headers['Location']
                print(f"🔗 Siguiendo redirección a: {redirect_url}")
                
                redirect_response = session.get(redirect_url, timeout=30)
                
                if redirect_response.status_code == 200:
                    # Analizar página de redirección
                    soup = BeautifulSoup(redirect_response.text, 'html.parser')
                    text_content = soup.get_text().lower()
                    
                    if 'success' in text_content or 'confirmed' in text_content:
                        print("🎉 ¡CAMBIO EXITOSO! (confirmado por redirección)")
                        return True
                    else:
                        print("❓ Redirección a página incierta")
                        return None
            else:
                print("❓ Redirección sin URL de destino")
                return None
                
        else:
            print(f"❌ Error del servidor: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error durante el proceso: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Función principal"""
    print("🔐 COMPLETADOR AUTOMÁTICO DE CAMBIO DE EMAIL")
    print("=" * 60)
    
    resultado = completar_cambio_email()
    
    print(f"\n📊 RESULTADO FINAL:")
    print("=" * 30)
    
    if resultado is True:
        print("🎉 ¡ÉXITO TOTAL!")
        print("✅ El cambio de email se completó correctamente")
        print("📧 Email cambiado de: <EMAIL>")
        print("📧 Email cambiado a: <EMAIL>")
    elif resultado is False:
        print("❌ FALLO EN EL CAMBIO")
        print("💡 Revisa los archivos generados para más detalles")
    else:
        print("❓ RESULTADO INCIERTO")
        print("💡 Requiere verificación manual")
    
    print(f"\n🏁 Proceso completado")


if __name__ == "__main__":
    main()
