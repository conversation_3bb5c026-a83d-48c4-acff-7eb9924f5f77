# Automatización Web a API - Gestión de Dominios/Correos

Este proyecto te permite capturar el flujo manual de un sitio web y generar código Python para replicarlo vía API.

## 🚀 Instalación

1. **Instalar Playwright** (ya instalado):
```bash
pip install playwright
python -m playwright install
```

## 📋 Proceso de Automatización

### Paso 1: Capturar el Flujo Web

Ejecuta el script de captura para analizar el sitio web:

```bash
python web_automation.py
```

**¿Qué hace este script?**
- Abre un navegador automatizado
- Te permite navegar manualmente por el sitio
- Captura todas las llamadas de red (requests/responses)
- Toma capturas de pantalla del proceso
- Guarda los datos para análisis posterior

**Durante la ejecución:**
1. Ingresa la URL del sitio web
2. Completa el login (automático o manual)
3. Navega por el sitio realizando las acciones que quieres automatizar
4. Presiona Enter cuando termines

**Archivos generados:**
- `network_requests_YYYYMMDD_HHMMSS.json` - Todas las peticiones HTTP
- `network_responses_YYYYMMDD_HHMMSS.json` - Todas las respuestas HTTP
- `screenshot_*.png` - Capturas de pantalla del proceso

### Paso 2: Generar Código API

Ejecuta el generador de API para crear código Python:

```bash
python api_generator.py
```

**¿Qué hace este script?**
- Analiza los datos de red capturados
- Identifica endpoints de API
- Detecta el proceso de autenticación
- Genera código Python para replicar las acciones
- Crea un reporte de análisis

**Archivos generados:**
- `generated_api_YYYYMMDD_HHMMSS.py` - Código Python para usar la API
- `analysis_report_YYYYMMDD_HHMMSS.md` - Reporte detallado del análisis

### Paso 3: Personalizar y Usar el Código

1. **Revisar el código generado:**
   - Ajustar nombres de campos según la API real
   - Verificar endpoints y parámetros
   - Implementar lógica de negocio específica

2. **Ejemplo de uso del código generado:**
```python
from generated_api_YYYYMMDD_HHMMSS import DomainEmailAPI

# Crear instancia de la API
api = DomainEmailAPI("https://tu-sitio-web.com")

# Realizar login
if api.login("tu_usuario", "tu_password"):
    # Ejecutar acciones automatizadas
    api.manage_domains()
```

## 🔧 Características

### Captura Web (`web_automation.py`)
- ✅ Navegador automatizado con Playwright
- ✅ Interceptación completa de red
- ✅ Login automático y manual
- ✅ Capturas de pantalla automáticas
- ✅ Modo interactivo para navegación manual

### Generador API (`api_generator.py`)
- ✅ Análisis automático de autenticación
- ✅ Extracción de endpoints de API
- ✅ Generación de código Python
- ✅ Reporte detallado de análisis
- ✅ Manejo de sesiones y cookies

## 📁 Estructura de Archivos

```
correos/
├── web_automation.py          # Script de captura web
├── api_generator.py           # Generador de código API
├── README.md                  # Este archivo
├── network_requests_*.json    # Datos de peticiones capturadas
├── network_responses_*.json   # Datos de respuestas capturadas
├── generated_api_*.py         # Código API generado
├── analysis_report_*.md       # Reportes de análisis
└── screenshot_*.png           # Capturas de pantalla
```

## 🎯 Casos de Uso

Este sistema es ideal para automatizar:
- ✅ Gestión de dominios
- ✅ Configuración de correos electrónicos
- ✅ Administración de DNS
- ✅ Procesos de registro/renovación
- ✅ Cualquier flujo web repetitivo

## ⚠️ Consideraciones Importantes

1. **Términos de Servicio:** Asegúrate de cumplir con los términos del sitio web
2. **Rate Limiting:** Implementa delays entre llamadas para evitar bloqueos
3. **Autenticación:** Guarda credenciales de forma segura
4. **Errores:** El código generado es un punto de partida, requiere ajustes
5. **Testing:** Prueba con datos de prueba antes de usar en producción

## 🔍 Troubleshooting

### Error: "No se encontraron archivos de datos"
- Ejecuta primero `web_automation.py` para capturar los datos

### Error: "Login automático falló"
- Usa el modo manual durante la captura
- Verifica los selectores de campos en el código

### Error: "Endpoints no detectados"
- Asegúrate de realizar acciones que generen llamadas AJAX/API
- Revisa los archivos JSON generados manualmente

### El código generado no funciona
- Revisa el reporte de análisis
- Ajusta los nombres de campos y parámetros
- Verifica las URLs y headers

## 📞 Soporte

Si encuentras problemas:
1. Revisa los archivos de log generados
2. Verifica las capturas de pantalla
3. Analiza el reporte de análisis
4. Ajusta el código según tus necesidades específicas

## 🚀 Próximos Pasos

Después de generar el código:
1. Testear cada endpoint individualmente
2. Implementar manejo de errores robusto
3. Agregar logging y monitoreo
4. Crear tests automatizados
5. Documentar la API específica de tu sitio
