# Automatización Dujaw.com - Gestión de Mailbox

Este proyecto automatiza completamente el acceso y gestión del mailbox temporal en dujaw.com, incluyendo unlock automático, monitoreo de mensajes y extracción de datos.

## ✅ Estado del Proyecto: COMPLETADO

- ✅ Análisis completo del sitio web
- ✅ Captura automática del flujo de autenticación
- ✅ API funcional para automatización
- ✅ Ejemplos de uso prácticos
- ✅ Documentación completa

## 🚀 Instalación

```bash
pip install requests beautifulsoup4
```

## 🎯 Sitio Objetivo

**URL:** https://dujaw.com/mailbox/<EMAIL>
**Password:** unlockgs2024
**Email:** <EMAIL>

## 📋 Uso Rápido

### Opción 1: API Lista para Usar (Recomendado)

```python
from dujaw_api_final import DujawAPI

# Crear instancia
api = DujawAPI()

# Realizar unlock automático
if api.unlock_mailbox():
    # Acceder al mailbox
    mailbox_info = api.access_mailbox()
    print(f"Mensajes: {len(mailbox_info['messages'])}")

    # Obtener estado
    status = api.get_mailbox_status()
    print(f"Estado: {status}")

# Cerrar sesión
api.logout()
```

### Opción 2: Ejemplo Completo con Monitoreo

```bash
python ejemplo_uso_dujaw.py
```

Este script incluye:
- ✅ Verificación de conectividad
- ✅ Obtención de información completa
- ✅ Guardado de estado en JSON
- ✅ Monitoreo automático de nuevos mensajes

## 📋 Proceso de Desarrollo (Ya Completado)

### ✅ Paso 1: Análisis del Sitio Web

Se analizó completamente el sitio dujaw.com:
- Estructura HTML del formulario de unlock
- Tokens CSRF requeridos
- Flujo de autenticación
- Endpoints disponibles

### ✅ Paso 2: Captura de Datos

Se capturaron todas las llamadas de red:
- GET inicial para obtener formulario
- POST para unlock con token CSRF
- Acceso al mailbox después de autenticación

### ✅ Paso 3: Desarrollo de API

Se desarrolló una API completa con:
- Autenticación automática
- Manejo de tokens CSRF
- Acceso al mailbox
- Parseo de contenido
- Manejo de errores

## 🔧 Características de la API

### DujawAPI (`dujaw_api_final.py`)
- ✅ **Autenticación automática** - Unlock automático con manejo de tokens CSRF
- ✅ **Acceso al mailbox** - Lectura completa del contenido del mailbox
- ✅ **Parseo inteligente** - Extracción automática de mensajes y acciones
- ✅ **Manejo de sesiones** - Gestión completa de cookies y autenticación
- ✅ **Monitoreo** - Verificación periódica de nuevos mensajes
- ✅ **Estado del mailbox** - Información detallada del estado actual
- ✅ **Manejo de errores** - Recuperación automática de errores comunes

### Herramientas de Desarrollo (Usadas para crear la API)
- ✅ **Captura web** - Scripts para analizar el sitio manualmente
- ✅ **Generador de API** - Herramientas para generar código desde capturas
- ✅ **Análisis de red** - Interceptación y análisis de llamadas HTTP

## 📁 Estructura de Archivos

```
correos/
├── dujaw_api_final.py         # 🎯 API principal lista para usar
├── ejemplo_uso_dujaw.py       # 📖 Ejemplos prácticos de uso
├── README.md                  # 📋 Esta documentación
│
├── # Herramientas de desarrollo (ya usadas)
├── simple_web_capture.py      # Captura web simplificada
├── dujaw_complete.py          # Automatización completa
├── api_generator.py           # Generador de código API
├── manual_capture_analyzer.py # Analizador de capturas manuales
│
├── # Datos capturados (ejemplos)
├── network_requests_*.json    # Peticiones HTTP capturadas
├── network_responses_*.json   # Respuestas HTTP capturadas
├── generated_api_*.py         # Código generado automáticamente
├── analysis_report_*.md       # Reportes de análisis
└── dujaw_response_*.html      # Páginas HTML capturadas
```

## 🎯 Casos de Uso Implementados

### ✅ Automatización de Mailbox Temporal
- **Unlock automático** - Acceso sin intervención manual
- **Monitoreo de mensajes** - Verificación periódica de nuevos emails
- **Extracción de datos** - Obtención automática de contenido
- **Gestión de sesiones** - Mantenimiento automático de autenticación

### 🔮 Casos de Uso Potenciales (Extensibles)
- **Notificaciones** - Alertas cuando lleguen mensajes específicos
- **Filtrado** - Procesamiento automático según remitente/asunto
- **Reenvío** - Envío automático a otros sistemas
- **Archivado** - Guardado automático de mensajes importantes
- **Integración** - Conexión con otros servicios/APIs

## ⚠️ Consideraciones Importantes

1. **Términos de Servicio:** Asegúrate de cumplir con los términos del sitio web
2. **Rate Limiting:** Implementa delays entre llamadas para evitar bloqueos
3. **Autenticación:** Guarda credenciales de forma segura
4. **Errores:** El código generado es un punto de partida, requiere ajustes
5. **Testing:** Prueba con datos de prueba antes de usar en producción

## 🔍 Troubleshooting

### Error: "No se encontraron archivos de datos"
- Ejecuta primero `web_automation.py` para capturar los datos

### Error: "Login automático falló"
- Usa el modo manual durante la captura
- Verifica los selectores de campos en el código

### Error: "Endpoints no detectados"
- Asegúrate de realizar acciones que generen llamadas AJAX/API
- Revisa los archivos JSON generados manualmente

### El código generado no funciona
- Revisa el reporte de análisis
- Ajusta los nombres de campos y parámetros
- Verifica las URLs y headers

## 📞 Soporte

Si encuentras problemas:
1. Revisa los archivos de log generados
2. Verifica las capturas de pantalla
3. Analiza el reporte de análisis
4. Ajusta el código según tus necesidades específicas

## 🚀 Próximos Pasos

Después de generar el código:
1. Testear cada endpoint individualmente
2. Implementar manejo de errores robusto
3. Agregar logging y monitoreo
4. Crear tests automatizados
5. Documentar la API específica de tu sitio
