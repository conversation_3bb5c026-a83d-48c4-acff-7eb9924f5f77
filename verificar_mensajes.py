#!/usr/bin/env python3
"""
Script para verificar y leer los 5 mensajes no leídos en dujaw.com
"""

import requests
import json
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


def analizar_mensajes_detallado():
    """Analiza los mensajes con más detalle"""
    print("🔍 ANÁLISIS DETALLADO DE MENSAJES")
    print("=" * 50)
    
    api = DujawAPI()
    
    try:
        # Realizar unlock
        if api.unlock_mailbox():
            print("✅ Unlock exitoso")
            
            # Acceder al mailbox
            response = api.session.get(api.mailbox_url)
            if response.status_code == 200:
                print("✅ Mailbox accedido")
                
                # Guardar HTML completo para análisis
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                html_file = f"mailbox_completo_{timestamp}.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"💾 HTML completo guardado en: {html_file}")
                
                # Parsear con BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Buscar diferentes tipos de elementos que podrían contener mensajes
                print("\n🔍 BUSCANDO ELEMENTOS DE MENSAJES...")
                
                # Buscar por clases comunes de email/mensaje
                message_selectors = [
                    '.message', '.email', '.mail', '.inbox-item',
                    '.msg', '.letter', '.correspondence',
                    '[class*="message"]', '[class*="email"]', '[class*="mail"]',
                    'tr', 'li', '.item', '.entry'
                ]
                
                all_potential_messages = []
                
                for selector in message_selectors:
                    try:
                        elements = soup.select(selector)
                        for element in elements:
                            text = element.get_text(strip=True)
                            if text and len(text) > 20:  # Filtrar elementos muy cortos
                                all_potential_messages.append({
                                    'selector': selector,
                                    'text': text,
                                    'html': str(element),
                                    'length': len(text)
                                })
                    except Exception as e:
                        print(f"Error con selector {selector}: {e}")
                
                print(f"📊 Elementos potenciales encontrados: {len(all_potential_messages)}")
                
                # Mostrar los elementos más prometedores
                if all_potential_messages:
                    # Ordenar por longitud de texto (los mensajes suelen ser más largos)
                    all_potential_messages.sort(key=lambda x: x['length'], reverse=True)
                    
                    print("\n📧 POSIBLES MENSAJES (ordenados por longitud):")
                    for i, msg in enumerate(all_potential_messages[:10], 1):  # Top 10
                        print(f"\n{i}. Selector: {msg['selector']}")
                        print(f"   Longitud: {msg['length']} caracteres")
                        print(f"   Texto: {msg['text'][:200]}...")
                        print(f"   HTML: {msg['html'][:150]}...")
                
                # Buscar específicamente elementos con números (como contadores de mensajes)
                print("\n🔢 BUSCANDO CONTADORES DE MENSAJES...")
                text_content = soup.get_text()
                
                # Buscar patrones que indiquen cantidad de mensajes
                import re
                number_patterns = [
                    r'(\d+)\s*(message|email|mail|unread)',
                    r'(message|email|mail|unread)\s*[:\s]*(\d+)',
                    r'(\d+)\s*(new|nuevo|sin leer)',
                    r'inbox\s*\((\d+)\)',
                    r'\((\d+)\)\s*(message|email)'
                ]
                
                for pattern in number_patterns:
                    matches = re.findall(pattern, text_content, re.IGNORECASE)
                    if matches:
                        print(f"✅ Patrón encontrado: {pattern}")
                        print(f"   Coincidencias: {matches}")
                
                # Buscar elementos que contengan "@" (direcciones de email)
                print("\n📧 BUSCANDO DIRECCIONES DE EMAIL...")
                email_elements = soup.find_all(text=re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'))
                for i, email_text in enumerate(email_elements[:5], 1):
                    print(f"  {i}. {email_text.strip()}")
                
                # Buscar tablas (los emails suelen estar en tablas)
                print("\n📋 BUSCANDO TABLAS...")
                tables = soup.find_all('table')
                print(f"Tablas encontradas: {len(tables)}")
                
                for i, table in enumerate(tables, 1):
                    rows = table.find_all('tr')
                    print(f"  Tabla {i}: {len(rows)} filas")
                    if rows:
                        # Mostrar primera fila como ejemplo
                        first_row_text = rows[0].get_text(strip=True)
                        print(f"    Primera fila: {first_row_text[:100]}...")
                
                # Buscar listas (ul, ol)
                print("\n📝 BUSCANDO LISTAS...")
                lists = soup.find_all(['ul', 'ol'])
                print(f"Listas encontradas: {len(lists)}")
                
                for i, list_elem in enumerate(lists, 1):
                    items = list_elem.find_all('li')
                    print(f"  Lista {i}: {len(items)} elementos")
                    if items:
                        first_item_text = items[0].get_text(strip=True)
                        print(f"    Primer elemento: {first_item_text[:100]}...")
                
                # Buscar divs con contenido significativo
                print("\n📦 BUSCANDO DIVS CON CONTENIDO...")
                divs = soup.find_all('div')
                content_divs = []
                
                for div in divs:
                    text = div.get_text(strip=True)
                    if len(text) > 50 and len(text) < 1000:  # Rango razonable para mensajes
                        content_divs.append({
                            'text': text,
                            'html': str(div)[:200]
                        })
                
                print(f"Divs con contenido relevante: {len(content_divs)}")
                for i, div in enumerate(content_divs[:5], 1):
                    print(f"  {i}. {div['text'][:100]}...")
                
            else:
                print(f"❌ Error accediendo mailbox: {response.status_code}")
                
        else:
            print("❌ Error en unlock")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        api.logout()


def buscar_mensajes_especificos():
    """Busca específicamente los 5 mensajes mencionados"""
    print("\n🎯 BÚSQUEDA ESPECÍFICA DE 5 MENSAJES")
    print("=" * 50)
    
    api = DujawAPI()
    
    try:
        if api.unlock_mailbox():
            response = api.session.get(api.mailbox_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Buscar texto que contenga "5" y palabras relacionadas con mensajes
            text_content = soup.get_text()
            
            import re
            patterns_5_messages = [
                r'5.*?(message|email|mail|unread|nuevo)',
                r'(message|email|mail|unread|nuevo).*?5',
                r'(\d+).*?(message|email|mail)',
                r'inbox.*?(\d+)',
                r'(\d+).*?inbox'
            ]
            
            print("🔍 Buscando patrones que indiquen 5 mensajes...")
            for pattern in patterns_5_messages:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    print(f"✅ Patrón '{pattern}' encontrado:")
                    for match in matches[:3]:  # Mostrar solo las primeras 3 coincidencias
                        print(f"   - {match}")
            
            # Buscar elementos que contengan el número 5
            elements_with_5 = soup.find_all(text=re.compile(r'5'))
            print(f"\n🔢 Elementos que contienen '5': {len(elements_with_5)}")
            
            for i, element in enumerate(elements_with_5[:10], 1):
                parent = element.parent if element.parent else None
                parent_text = parent.get_text(strip=True) if parent else "Sin padre"
                print(f"  {i}. '{element.strip()}' en: {parent_text[:100]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        api.logout()


if __name__ == "__main__":
    print("🚀 VERIFICACIÓN DE MENSAJES EN DUJAW.COM")
    print("=" * 60)
    
    # Análisis detallado
    analizar_mensajes_detallado()
    
    # Búsqueda específica
    buscar_mensajes_especificos()
    
    print("\n🏁 Análisis completado")
    print("📁 Revisa el archivo HTML generado para análisis manual")
